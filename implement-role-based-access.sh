#!/bin/bash

# Implementation script for role-based access control and organization management

echo "🔧 Implementing Role-Based Access Control and Organization Management"
echo "=================================================================="

cd /Users/<USER>/hat/PSHUB

# Function to backup and replace files
backup_and_replace() {
    local source_file=$1
    local target_file=$2
    
    if [ -f "$target_file" ]; then
        echo "📦 Backing up $target_file"
        cp "$target_file" "$target_file.backup.$(date +%Y%m%d_%H%M%S)"
    fi
    
    echo "🔄 Replacing $target_file"
    cp "$source_file" "$target_file"
}

# Backend Updates
echo "🔧 Updating Backend Files..."

# Update Agent Entity
backup_and_replace "nestbe/src/agent/agent.entity.updated.ts" "nestbe/src/agent/agent.entity.ts"

# Update Agent Service
backup_and_replace "nestbe/src/agent/agent.service.updated.ts" "nestbe/src/agent/agent.service.ts"

# Update Agent Controller
backup_and_replace "nestbe/src/agent/agent.controller.updated.ts" "nestbe/src/agent/agent.controller.ts"

# Update DTOs
backup_and_replace "nestbe/src/agent/dto/create-agent.dto.updated.ts" "nestbe/src/agent/dto/create-agent.dto.ts"
backup_and_replace "nestbe/src/agent/dto/update-agent.dto.updated.ts" "nestbe/src/agent/dto/update-agent.dto.ts"

# Update Organization Service
backup_and_replace "nestbe/src/organization/organization.service.updated.ts" "nestbe/src/organization/organization.service.ts"

# Update Organization Controller
backup_and_replace "nestbe/src/organization/organization.controller.updated.ts" "nestbe/src/organization/organization.controller.ts"

# Frontend Updates
echo "🎨 Updating Frontend Files..."

# Update Agent List Component
backup_and_replace "webapp/src/components/AgentList.updated.vue" "webapp/src/components/AgentList.vue"

# Update Dashboard Component
backup_and_replace "webapp/src/components/Dashboard.updated.vue" "webapp/src/components/Dashboard.vue"

# Create database migration for role column
echo "📊 Creating database migration..."
cd nestbe

cat > src/migrations/$(date +%Y%m%d%H%M%S)-add-role-to-agent.ts << 'EOF'
import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddRoleToAgent$(date +%Y%m%d%H%M%S) implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        // Add role column to agent table
        await queryRunner.addColumn('agent', new TableColumn({
            name: 'role',
            type: 'enum',
            enum: ['admin', 'agent'],
            default: "'agent'",
            isNullable: false,
        }));

        // Add address column to agent table
        await queryRunner.addColumn('agent', new TableColumn({
            name: 'address',
            type: 'varchar',
            isNullable: true,
        }));

        // Update existing agents to have agent role by default
        await queryRunner.query(`UPDATE agent SET role = 'agent' WHERE role IS NULL`);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.dropColumn('agent', 'role');
        await queryRunner.dropColumn('agent', 'address');
    }
}
EOF

# Update package.json to include new dependencies if needed
echo "📦 Checking dependencies..."

# Check if class-validator is installed
if ! yarn list class-validator > /dev/null 2>&1; then
    echo "📦 Installing class-validator..."
    yarn add class-validator class-transformer
fi

# Build and test backend
echo "🔨 Building backend..."
if yarn build; then
    echo "✅ Backend build successful"
else
    echo "❌ Backend build failed"
    exit 1
fi

# Frontend updates
cd ../webapp

# Update router to include new routes
echo "🛣️ Updating router..."
cat >> src/router/index.ts << 'EOF'

// Add new routes for organization and admin management
{
  path: '/organizations',
  name: 'organizations',
  component: () => import('../views/OrganizationManagement.vue'),
  meta: { requiresAuth: true, requiresAdmin: true }
},
{
  path: '/admins',
  name: 'admins', 
  component: () => import('../views/AdminManagement.vue'),
  meta: { requiresAuth: true, requiresAdmin: true }
},
{
  path: '/agents/create',
  name: 'create-agent',
  component: () => import('../views/CreateAgent.vue'),
  meta: { requiresAuth: true }
},
{
  path: '/agents/:id',
  name: 'agent-details',
  component: () => import('../views/AgentDetails.vue'),
  meta: { requiresAuth: true }
}
EOF

# Create view components
echo "📄 Creating view components..."

mkdir -p src/views

# Create OrganizationManagement view
cat > src/views/OrganizationManagement.vue << 'EOF'
<template>
  <div class="container mx-auto px-4 py-8">
    <OrganizationManagement />
  </div>
</template>

<script setup lang="ts">
import OrganizationManagement from '../components/OrganizationManagement.vue';
</script>
EOF

# Create AdminManagement view
cat > src/views/AdminManagement.vue << 'EOF'
<template>
  <div class="container mx-auto px-4 py-8">
    <AdminList />
  </div>
</template>

<script setup lang="ts">
import AdminList from '../components/AdminList.vue';
</script>
EOF

# Update axios interceptor for organization context
echo "🔧 Updating axios configuration..."
cat >> src/utils/axios.ts << 'EOF'

// Add organization context to requests
axios.interceptors.request.use((config) => {
  const organizationId = localStorage.getItem('currentOrganizationId') || 'HUB-1';
  if (config.headers) {
    config.headers['X-Organization-ID'] = organizationId;
  }
  return config;
});
EOF

# Build frontend
echo "🔨 Building frontend..."
if npm run build; then
    echo "✅ Frontend build successful"
else
    echo "❌ Frontend build failed"
    exit 1
fi

cd ..

echo ""
echo "🎉 Implementation Complete!"
echo "=========================="
echo ""
echo "✅ Backend Updates:"
echo "   - Added role-based access control (admin/agent)"
echo "   - Updated agent entity with role and address fields"
echo "   - Added organization filtering for agents"
echo "   - Created admin-only endpoints"
echo "   - Added pagination to organization management"
echo ""
echo "✅ Frontend Updates:"
echo "   - Fixed agent name and organization display"
echo "   - Added role-based navigation tabs"
echo "   - Created organization management interface"
echo "   - Added admin management section"
echo "   - Implemented inline editing for organizations"
echo ""
echo "🔄 Next Steps:"
echo "   1. Run database migrations: cd nestbe && yarn typeorm migration:run"
echo "   2. Restart both applications"
echo "   3. Test role-based access control"
echo "   4. Create admin users if needed"
echo ""
echo "📋 Features Implemented:"
echo "   ✅ Role-based access (Admin/Agent)"
echo "   ✅ Organization management tab (Admin only)"
echo "   ✅ Admin management section (Admin only)"
echo "   ✅ Organization-based data filtering"
echo "   ✅ Inline organization editing"
echo "   ✅ Fixed agent name/organization display"
echo "   ✅ Removed organization switching for agents"
echo ""
echo "🚀 Ready to test!"
