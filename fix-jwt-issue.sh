#!/bin/bash

# Quick fix for JWT_SECRET issue

echo "🔧 Fixing JWT_SECRET issue..."

cd /Users/<USER>/hat/PSHUB/nestbe

# Ensure .env file exists with correct content
cat > .env << 'EOF'
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=hubsolution
DB_PASSWORD=9080
DB_DATABASE=hubsolution

# JWT Configuration
JWT_SECRET=0473853d2c575eca150f3802cafbd035dd04754d4f9796274455b1a15223d116
JWT_EXPIRES_IN=24h

# Application Configuration
NODE_ENV=development
PORT=3000

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
EOF

echo "✅ .env file created with JWT_SECRET"

# Test if the environment variable is being read
echo "🔍 Testing environment variable loading..."
node -e "
require('dotenv').config();
console.log('JWT_SECRET from process.env:', process.env.JWT_SECRET ? 'FOUND' : 'NOT FOUND');
console.log('NODE_ENV:', process.env.NODE_ENV);
"

# Install dotenv if not present
if ! npm list dotenv > /dev/null 2>&1; then
    echo "📦 Installing dotenv..."
    yarn add dotenv
fi

echo "🚀 Now try starting the backend with: yarn start:dev"
