#!/bin/bash

# Emergency fix for PSHUB application

echo "🚨 EMERGENCY FIX FOR PSHUB APPLICATION"
echo "======================================"

cd /Users/<USER>/hat/PSHUB

# Kill any existing processes
echo "🛑 Stopping existing processes..."
pkill -f "yarn start:dev" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true
lsof -ti:3000 | xargs -r kill -9 2>/dev/null || true
lsof -ti:5173 | xargs -r kill -9 2>/dev/null || true

# Fix backend environment
echo "🔧 Fixing backend environment..."
cd nestbe

# Create proper .env file
cat > .env << 'EOF'
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=hubsolution
DB_PASSWORD=9080
DB_DATABASE=hubsolution

# JWT Configuration
JWT_SECRET=0473853d2c575eca150f3802cafbd035dd04754d4f9796274455b1a15223d116
JWT_EXPIRES_IN=24h

# Application Configuration
NODE_ENV=development
PORT=3000

# CORS Configuration
CORS_ORIGIN=http://localhost:5173
EOF

echo "✅ Environment file created"

# Install dotenv if missing
if ! yarn list dotenv > /dev/null 2>&1; then
    echo "📦 Installing dotenv..."
    yarn add dotenv
fi

# Fix JWT strategy with more robust version
echo "🔧 Fixing JWT strategy..."
cp src/auth/strategies/jwt.strategy.ts src/auth/strategies/jwt.strategy.backup.ts
cp src/auth/strategies/jwt.strategy.fixed.ts src/auth/strategies/jwt.strategy.ts

# Test environment loading
echo "🔍 Testing environment variables..."
node -e "
require('dotenv').config();
console.log('✅ JWT_SECRET:', process.env.JWT_SECRET ? 'LOADED' : 'MISSING');
console.log('✅ NODE_ENV:', process.env.NODE_ENV || 'development');
console.log('✅ PORT:', process.env.PORT || '3000');
"

# Start backend
echo "🚀 Starting backend..."
yarn start:dev &
BACKEND_PID=$!

# Wait for backend to start
echo "⏳ Waiting for backend to initialize..."
sleep 10

# Check if backend is running
if curl -s http://localhost:3000/api > /dev/null 2>&1; then
    echo "✅ Backend is running successfully!"
else
    echo "❌ Backend failed to start. Checking logs..."
    sleep 5
fi

# Start frontend
echo "🎨 Starting frontend..."
cd ../webapp
npm run dev &
FRONTEND_PID=$!

echo ""
echo "🎉 Emergency fix applied!"
echo ""
echo "📱 Access URLs:"
echo "   Frontend: http://localhost:5173"
echo "   Backend:  http://localhost:3000"
echo "   API Docs: http://localhost:3000/api"
echo ""
echo "🔧 If backend still fails:"
echo "   1. Check PostgreSQL is running"
echo "   2. Verify database credentials"
echo "   3. Check the terminal for error messages"
echo ""
echo "Press Ctrl+C to stop both servers"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping servers..."
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    lsof -ti:3000 | xargs -r kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs -r kill -9 2>/dev/null || true
    echo "✅ Cleanup complete"
}

trap cleanup EXIT INT TERM

# Wait for user input
wait
