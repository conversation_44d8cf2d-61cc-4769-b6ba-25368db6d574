#!/bin/bash

# PSHUB Development Server Startup Script

echo "🚀 Starting PSHUB Development Environment..."

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  Port $1 is already in use"
        return 1
    else
        echo "✅ Port $1 is available"
        return 0
    fi
}

# Check required ports
echo "🔍 Checking ports..."
check_port 3000 || echo "Backend port 3000 is busy"
check_port 5173 || echo "Frontend port 5173 is busy"

# Start backend
echo "🔧 Starting NestJS Backend..."
cd nestbe
if [ ! -d "node_modules" ]; then
    echo "📦 Installing backend dependencies..."
    yarn install
fi

echo "🏃 Starting backend development server..."
yarn start:dev &
BACKEND_PID=$!

# Wait a moment for backend to start
sleep 5

# Start frontend
echo "🎨 Starting Vue.js Frontend..."
cd ../webapp
if [ ! -d "node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    npm install
fi

echo "🏃 Starting frontend development server..."
npm run dev &
FRONTEND_PID=$!

echo "🎉 Development servers started!"
echo "📱 Frontend: http://localhost:5173"
echo "🔧 Backend: http://localhost:3000"
echo "📚 API Docs: http://localhost:3000/api"

# Function to cleanup on exit
cleanup() {
    echo "🛑 Stopping development servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ Cleanup complete"
}

# Set trap to cleanup on script exit
trap cleanup EXIT

# Wait for user input to stop
echo "Press Ctrl+C to stop all servers..."
wait
