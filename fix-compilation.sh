#!/bin/bash

echo "🔧 Fixing compilation errors by copying final working files..."

cd /Users/<USER>/hat/PSHUB/nestbe/src

# Remove existing problematic files
echo "🗑️ Removing existing files..."
rm -f agent/agent.controller.ts
rm -f agent/agent.service.ts
rm -f organization/organization.controller.ts
rm -f organization/organization.service.ts

# Copy final working versions
echo "📁 Copying final working files..."

# Agent files
if [ -f "agent/agent.controller.final.ts" ]; then
    cp agent/agent.controller.final.ts agent/agent.controller.ts
    echo "✅ Copied agent.controller.ts"
else
    echo "❌ agent.controller.final.ts not found"
fi

if [ -f "agent/agent.service.final.ts" ]; then
    cp agent/agent.service.final.ts agent/agent.service.ts
    echo "✅ Copied agent.service.ts"
else
    echo "❌ agent.service.final.ts not found"
fi

# Organization files
if [ -f "organization/organization.controller.final.ts" ]; then
    cp organization/organization.controller.final.ts organization/organization.controller.ts
    echo "✅ Copied organization.controller.ts"
else
    echo "❌ organization.controller.final.ts not found"
fi

if [ -f "organization/organization.service.final.ts" ]; then
    cp organization/organization.service.final.ts organization/organization.service.ts
    echo "✅ Copied organization.service.ts"
else
    echo "❌ organization.service.final.ts not found"
fi

# Clean up extra files
echo "🧹 Cleaning up extra files..."
rm -f agent/agent.controller.*.ts
rm -f agent/agent.service.*.ts
rm -f organization/organization.controller.*.ts
rm -f organization/organization.service.*.ts

echo ""
echo "✅ Compilation fix complete!"
echo "🔨 Now try: cd /Users/<USER>/hat/PSHUB/nestbe && yarn build"
