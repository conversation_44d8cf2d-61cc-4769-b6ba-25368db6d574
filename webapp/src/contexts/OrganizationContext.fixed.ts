import { ref, readonly } from 'vue';
import axios from '../utils/axios';

interface Organization {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
}

const currentOrganization = ref<Organization | null>(null);
const isLoading = ref(false);

const loadDefaultOrganization = async () => {
  // Only load if user is authenticated
  const token = localStorage.getItem('token');
  if (!token) {
    console.log('No token found, skipping organization load');
    return;
  }

  try {
    isLoading.value = true;
    const response = await axios.get('/api/organizations/default');
    currentOrganization.value = response.data;
    localStorage.setItem('currentOrganizationId', response.data.id);
  } catch (error) {
    console.error('Error loading default organization:', error);
    // If unauthorized, don't retry
    if (error.response?.status === 401) {
      console.log('Unauthorized - user needs to login');
      return;
    }
  } finally {
    isLoading.value = false;
  }
};

const loadOrganizationById = async (orgId: string) => {
  // Only load if user is authenticated
  const token = localStorage.getItem('token');
  if (!token) {
    console.log('No token found, skipping organization load');
    return;
  }

  try {
    isLoading.value = true;
    const response = await axios.get(`/api/organizations/${orgId}`);
    currentOrganization.value = response.data;
    localStorage.setItem('currentOrganizationId', response.data.id);
  } catch (error) {
    console.error('Error loading organization:', error);
    // If there's an error, try to load the default organization
    if (error.response?.status !== 401) {
      await loadDefaultOrganization();
    }
  } finally {
    isLoading.value = false;
  }
};

export const useOrganization = () => {
  const setOrganization = (org: Organization) => {
    currentOrganization.value = org;
    localStorage.setItem('currentOrganizationId', org.id);
  };

  const getOrganization = () => {
    return readonly(currentOrganization);
  };

  const getOrganizationId = () => {
    return currentOrganization.value?.id;
  };

  const initializeOrganization = async () => {
    // Check if user is authenticated first
    const token = localStorage.getItem('token');
    if (!token) {
      console.log('No authentication token found');
      return;
    }

    // Check if we already have an organization loaded
    if (currentOrganization.value) {
      return;
    }

    // Try to load from stored organization ID
    const storedOrgId = localStorage.getItem('currentOrganizationId');
    if (storedOrgId) {
      await loadOrganizationById(storedOrgId);
    } else {
      await loadDefaultOrganization();
    }
  };

  const clearOrganization = () => {
    currentOrganization.value = null;
    localStorage.removeItem('currentOrganizationId');
  };

  const getLoadingState = () => {
    return readonly(isLoading);
  };

  return {
    setOrganization,
    getOrganization,
    getOrganizationId,
    initializeOrganization,
    clearOrganization,
    getLoadingState
  };
};
