<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Sign in to your account
        </h2>
      </div>
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="email" class="sr-only">Email address</label>
            <input
              id="email"
              v-model="email"
              name="email"
              type="email"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              placeholder="Email address"
            />
          </div>
          <div>
            <label for="password" class="sr-only">Password</label>
            <input
              id="password"
              v-model="password"
              name="password"
              type="password"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              placeholder="Password"
            />
          </div>
        </div>

        <div v-if="errorMessage" class="text-red-600 text-sm text-center">
          {{ errorMessage }}
        </div>

        <div>
          <button
            type="submit"
            :disabled="isLoading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <span v-if="isLoading">Signing in...</span>
            <span v-else>Sign in</span>
          </button>
        </div>

        <div class="text-sm text-center">
          <router-link to="/register" class="font-medium text-indigo-600 hover:text-indigo-500">
            Don't have an account? Register
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { useRouter } from 'vue-router';
import axios from '../utils/axios';
import { useOrganization } from '../contexts/OrganizationContext';

export default defineComponent({
  name: 'Login',
  setup() {
    const router = useRouter();
    const { initializeOrganization } = useOrganization();
    const email = ref('');
    const password = ref('');
    const isLoading = ref(false);
    const errorMessage = ref('');

    const handleLogin = async () => {
      try {
        isLoading.value = true;
        errorMessage.value = '';

        // Get organization ID from URL query parameters
        const urlParams = new URLSearchParams(window.location.search);
        const organizationId = urlParams.get('organizationId');

        const response = await axios.post('/api/auth/login', {
          email: email.value,
          password: password.value,
        }, {
          params: {
            organizationId
          }
        });

        const { access_token, user } = response.data;
        
        // Store authentication data
        localStorage.setItem('token', access_token);
        localStorage.setItem('user', JSON.stringify(user));
        
        // Store organization ID if provided
        if (organizationId) {
          localStorage.setItem('currentOrganizationId', organizationId);
        }

        // Initialize organization context after successful login
        await initializeOrganization();
        
        // Redirect to dashboard
        router.push('/');
      } catch (error) {
        console.error('Login failed:', error);
        isLoading.value = false;
        
        if (error.response?.status === 401) {
          errorMessage.value = 'Invalid email or password';
        } else {
          errorMessage.value = 'Login failed. Please try again.';
        }
      }
    };

    return {
      email,
      password,
      isLoading,
      errorMessage,
      handleLogin,
    };
  },
});
</script>
