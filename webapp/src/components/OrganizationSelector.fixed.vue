<template>
  <div class="organization-selector">
    <!-- Floating Button -->
    <button 
      class="org-selector-btn"
      @click="showModal = true"
    >
      <i class="fas fa-building"></i>
      {{ currentOrganization?.name || 'Select Organization' }}
    </button>

    <!-- Modal -->
    <div v-if="showModal" class="modal-overlay" @click="showModal = false">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h2>Select Organization</h2>
          <button class="close-btn" @click="showModal = false">&times;</button>
        </div>
        <div class="modal-body">
          <div v-if="isLoading" class="text-center py-4">
            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <p class="mt-2 text-gray-600">Loading organizations...</p>
          </div>
          
          <div v-else-if="organizations.length === 0" class="text-center py-4">
            <p class="text-gray-600">No organizations available</p>
          </div>
          
          <div v-else class="search-box">
            <select
              v-model="currentOrganizationId"
              @change="onOrganizationSelected"
              class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option disabled value="">Please select an organization</option>
              <option v-for="org in organizations" :key="org.id" :value="org.id">
                {{ org.name }}
              </option>
            </select>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, computed, watch } from 'vue';
import axios from '../utils/axios'; // Use the configured axios instance
import { useOrganization } from '../contexts/OrganizationContext';

interface Organization {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
}

export default defineComponent({
  name: 'OrganizationSelector',
  setup() {
    const organizations = ref<Organization[]>([]);
    const showModal = ref(false);
    const isLoading = ref(false);
    const { setOrganization, getOrganization } = useOrganization();
    const currentOrganization = computed(() => getOrganization().value);
    const currentOrganizationId = ref<string | null>(null);

    watch(currentOrganization, (newOrg) => {
      console.log('OrganizationSelector: currentOrganization changed to', newOrg);
      currentOrganizationId.value = newOrg?.id || null;
    }, { immediate: true });

    const fetchOrganizations = async () => {
      // Check if user is authenticated
      const token = localStorage.getItem('token');
      if (!token) {
        console.log('OrganizationSelector: No token found, skipping organization fetch');
        return;
      }

      try {
        isLoading.value = true;
        console.log('OrganizationSelector: Fetching organizations...');
        
        // Use relative path to go through Vite proxy
        const response = await axios.get('/api/organizations');
        
        console.log('OrganizationSelector: API response:', response.data);
        
        if (response.data && Array.isArray(response.data.data)) {
          organizations.value = response.data.data;
          console.log('OrganizationSelector: Organizations fetched:', organizations.value);
        } else if (Array.isArray(response.data)) {
          // Handle case where data is directly an array
          organizations.value = response.data;
          console.log('OrganizationSelector: Organizations fetched (direct array):', organizations.value);
        } else {
          console.warn('API response for organizations is not an array:', response.data);
          organizations.value = [];
        }
        
        const savedOrgId = localStorage.getItem('currentOrganizationId');
        console.log('OrganizationSelector: Saved organization ID from localStorage:', savedOrgId);
        
        if (savedOrgId && organizations.value.length > 0) {
          const org = organizations.value.find(org => org.id === savedOrgId);
          if (org) {
            setOrganization(org);
            currentOrganizationId.value = org.id;
            console.log('OrganizationSelector: Set organization from localStorage:', org);
          }
        } else if (organizations.value.length > 0) {
          setOrganization(organizations.value[0]);
          currentOrganizationId.value = organizations.value[0].id;
          console.log('OrganizationSelector: Set default organization:', organizations.value[0]);
        }
      } catch (error) {
        console.error('OrganizationSelector: Error fetching organizations:', error);
        
        // If it's an authentication error, don't retry
        if (error.response?.status === 401) {
          console.log('OrganizationSelector: Authentication error - user needs to login');
        }
      } finally {
        isLoading.value = false;
      }
    };

    const selectOrganization = (org: Organization) => {
      console.log('OrganizationSelector: Selecting organization:', org);
      setOrganization(org);
      showModal.value = false;
      
      // Reload the page and navigate to cases
      window.location.href = '/dashboard?tab=cases';
    };

    const onOrganizationSelected = (event: Event) => {
      const selectedId = (event.target as HTMLSelectElement).value;
      console.log('OrganizationSelector: Dropdown selected ID:', selectedId);
      const org = organizations.value.find(o => o.id === selectedId);
      if (org) {
        selectOrganization(org);
      }
    };

    onMounted(() => {
      fetchOrganizations();
    });

    return {
      organizations,
      currentOrganization,
      showModal,
      isLoading,
      selectOrganization,
      onOrganizationSelected,
      currentOrganizationId
    };
  }
});
</script>

<style scoped>
.organization-selector {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
}

.org-selector-btn {
  background-color: #4CAF50;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 25px;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0,0,0,0.2);
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
}

.org-selector-btn:hover {
  background-color: #45a049;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  padding: 16px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.5rem;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 16px;
}

.search-box {
  margin-bottom: 16px;
}

.organization-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.organization-item {
  padding: 12px;
  border: 1px solid #eee;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.organization-item:hover {
  background-color: #f5f5f5;
}

.organization-item.active {
  background-color: #e3f2fd;
  border-color: #2196F3;
}

.org-name {
  font-weight: 500;
  margin-bottom: 4px;
}

.org-description {
  font-size: 12px;
  color: #666;
}
</style>
