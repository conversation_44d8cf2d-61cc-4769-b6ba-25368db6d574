<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Navigation Bar -->
    <nav class="bg-white shadow-lg">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex">
            <!-- Logo/Brand -->
            <div class="flex-shrink-0 flex items-center">
              <h1 class="text-xl font-bold text-gray-800"></h1>
            </div>

            <!-- Navigation Links -->
            <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
              <button
                v-for="tab in tabs"
                :key="tab.id"
                @click="activeTab = tab.id"
                :class="[
                  activeTab === tab.id
                    ? 'border-indigo-500 text-gray-900'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                  'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium'
                ]"
              >
                {{ tab.name }}
              </button>
            </div>
          </div>

          <!-- Right side of nav -->
        </div>
      </div>

      <!-- Mobile menu -->
      <div class="sm:hidden">
        <div class="pt-2 pb-3 space-y-1">
          <button
            v-for="tab in tabs"
            :key="tab.id"
            @click="activeTab = tab.id"
            :class="[
              activeTab === tab.id
                ? 'bg-indigo-50 border-indigo-500 text-indigo-700'
                : 'border-transparent text-gray-500 hover:bg-gray-50 hover:border-gray-300 hover:text-gray-700',
              'block pl-3 pr-4 py-2 border-l-4 text-base font-medium'
            ]"
          >
            {{ tab.name }}
          </button>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Progress Status Tabs -->
      <div v-if="activeTab === 'cases'" class="mb-4 flex justify-between bg-white shadow rounded-lg p-4">
        <div class="flex space-x-2 border-gray-200">
          <button
            v-for="stage in progressStages"
            :key="stage.id"
            @click="activeProgressStage = stage.id; fetchCasesByStage()"
            :class="[
              activeProgressStage === stage.id
                ? 'border-indigo-500 text-indigo-600'
                : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
              'py-2 px-4 text-sm font-medium border-b-2'
            ]"
          >
            {{ stage.name }}
          </button>
        </div>
        <div class="flex items-center space-x-4">
            <button
              @click="toggleSearchForm"
              class="text-gray-500 hover:text-gray-700"
            >
              🔍 Search
            </button>

            <!-- Case Tab Actions -->
            <template v-if="activeTab === 'cases'">
              <button
                @click="goToOcrImport"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                Import from OCR
              </button>
              <button
                @click="createNewCase"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Create New Case
              </button>
            </template>
          </div>
      </div>
      <!-- Patient Tab Actions -->
      <div v-if="activeTab === 'patients'" class="mb-4 flex justify-between bg-white shadow rounded-lg p-4">
        <div class="flex items-center space-x-4">
          <button
            @click="toggleSearchForm"
            class="text-gray-500 hover:text-gray-700"
          >
            🔍 Search
          </button>
        </div>
        <div class="flex items-center space-x-4">
          <button
            @click="createNewPatient"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Create New Patient
          </button>
        </div>
      </div>

      <!-- Prescriber Tab Actions -->
      <div v-if="activeTab === 'prescribers'" class="mb-4 flex justify-between bg-white shadow rounded-lg p-4">
        <div class="flex items-center space-x-4">
          <button
            @click="toggleSearchForm"
            class="text-gray-500 hover:text-gray-700"
          >
            🔍 Search
          </button>
        </div>
        <div class="flex items-center space-x-4">
          <button
            @click="createNewPrescriber"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Create New Prescriber
          </button>
        </div>
      </div>

      <!-- Agent Tab Actions -->
      <div v-if="activeTab === 'agents'" class="mb-4 flex justify-between bg-white shadow rounded-lg p-4">
        <div class="flex items-center space-x-4">
          <button
            @click="toggleSearchForm"
            class="text-gray-500 hover:text-gray-700"
          >
            🔍 Search
          </button>
        </div>
        <div class="flex items-center space-x-4">
          <button
            @click="createNewAgent"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Create New Agent
          </button>
          <button
            @click="openAgentListAutoAssignModal"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            Auto Assign Agent
          </button>
          <button
            @click="manualAssignAgent"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700"
          >
            Manual Assign Agent
          </button>
          <button
            @click="goToAgents"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            View All Agents
          </button>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="mb-4 flex justify-between" v-if="activeTab === 'cases'">

        <div class="flex space-x-4">

        </div>
      </div>

      <!-- Search Forms -->
      <div v-if="showSearchForm" class="mb-6">
        <div class="bg-white shadow rounded-lg p-6">
          <!-- Cases Search Form -->
          <form v-if="activeTab === 'cases'" @submit.prevent="submitCaseSearch" class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Search Cases</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
              <div v-for="(label, key) in caseSearchFormLabels" :key="key">
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ label }}</label>
                <input
                  v-model="caseSearchFormData[key]"
                  type="text"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div class="pt-4 flex space-x-4">
              <button
                type="submit"
                class="px-6 py-2 bg-blue-600 text-white font-semibold rounded hover:bg-blue-700 transition duration-150"
              >
                🔍 Search Cases
              </button>
              <button
                type="button"
                @click="clearCaseSearch"
                class="px-6 py-2 bg-gray-600 text-white font-semibold rounded hover:bg-gray-700 transition duration-150"
              >
                Clear
              </button>
            </div>
          </form>

          <!-- Patients Search Form -->
          <form v-if="activeTab === 'patients'" @submit.prevent="submitPatientSearch" class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Search Patients</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
              <div v-for="(label, key) in patientSearchFormLabels" :key="key">
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ label }}</label>
                <input
                  v-model="patientSearchFormData[key]"
                  :type="key === 'dateOfBirth' ? 'date' : 'text'"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div class="pt-4 flex space-x-4">
              <button
                type="submit"
                class="px-6 py-2 bg-blue-600 text-white font-semibold rounded hover:bg-blue-700 transition duration-150"
              >
                🔍 Search Patients
              </button>
              <button
                type="button"
                @click="clearPatientSearch"
                class="px-6 py-2 bg-gray-600 text-white font-semibold rounded hover:bg-gray-700 transition duration-150"
              >
                Clear
              </button>
            </div>
          </form>

          <!-- Prescribers Search Form -->
          <form v-if="activeTab === 'prescribers'" @submit.prevent="submitPrescriberSearch" class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Search Prescribers</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
              <div v-for="(label, key) in prescriberSearchFormLabels" :key="key">
                <label class="block text-sm font-medium text-gray-700 mb-1">{{ label }}</label>
                <input
                  v-model="prescriberSearchFormData[key]"
                  type="text"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div class="pt-4 flex space-x-4">
              <button
                type="submit"
                class="px-6 py-2 bg-blue-600 text-white font-semibold rounded hover:bg-blue-700 transition duration-150"
              >
                🔍 Search Prescribers
              </button>
              <button
                type="button"
                @click="clearPrescriberSearch"
                class="px-6 py-2 bg-gray-600 text-white font-semibold rounded hover:bg-gray-700 transition duration-150"
              >
                Clear
              </button>
            </div>
          </form>

          <!-- Agent Search Form -->
          <form @submit.prevent="submitAgentSearch" class="space-y-4">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Search Agents</h3>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Name</label>
                <input
                  v-model="agentSearchFormData.name"
                  type="text"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Organization</label>
                <input
                  v-model="agentSearchFormData.organization"
                  type="text"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Skills</label>
                <input
                  v-model="agentSearchFormData.skills"
                  type="text"
                  class="block w-full border border-gray-300 rounded-md shadow-sm px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>
            <div class="pt-4 flex space-x-4">
              <button
                type="submit"
                class="px-6 py-2 bg-blue-600 text-white font-semibold rounded hover:bg-blue-700 transition duration-150"
              >
                🔍 Search Agents
              </button>
              <button
                type="button"
                @click="clearAgentSearch"
                class="px-6 py-2 bg-gray-600 text-white font-semibold rounded hover:bg-gray-700 transition duration-150"
              >
                Clear
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Content -->
      <div class="bg-white shadow rounded-lg">
        <!-- Cases Table -->
        <div v-if="activeTab === 'cases'" class="overflow-x-auto">
          <div v-if="(cases?.length??0) === 0" class="text-center py-8">
            <p class="text-gray-500 text-lg">No cases available</p>
            <div class="flex justify-center space-x-4 mt-4">
              <button
                @click="createNewCase"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
              >
                Create New Case
              </button>
              <button
                @click="goToOcrImport"
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
              >
                Import from OCR
              </button>
            </div>
          </div>
          <table v-else class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Case ID</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Case Type</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Patient</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Prescriber</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Drug Family</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="caseItem in cases" :key="caseItem.caseId">
                <td class="px-6 py-4 whitespace-nowrap">
                  <router-link :to="{ name: 'case-details', params: { caseId: caseItem.caseId } }" class="text-indigo-600 hover:text-indigo-900">
                    {{ caseItem.caseId }}
                  </router-link>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">{{ caseItem.caseType }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ caseItem.enrollmentStatus }}</td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {{ caseItem.patient?.firstName || '' }} {{ caseItem.patient?.middleName || '' }} {{ caseItem.patient?.lastName || '' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {{ caseItem.prescriber?.firstName || '' }} {{ caseItem.prescriber?.lastName || '' }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">{{ caseItem.drugFamily?.name || '' }}</td>
                <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(caseItem.createdDate) }}</td>
                <td class="px-6 py-4 flex whitespace-nowrap text-sm font-medium">
                  <router-link
                    :to="{ name: 'case-details', params: { caseId: caseItem.caseId } }"
                    class="text-indigo-600 hover:text-indigo-900 mr-3"
                    title="Edit Case"
                  >
                    <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"
                    />
                    <path
                      fill-rule="evenodd"
                      d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  </router-link>
                  <button
                    @click="deleteCase(caseItem.caseId)"
                    class="text-red-600 hover:text-red-900"
                    title="Delete Case"
                  >
                    <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Pagination for Cases -->
          <Pagination
            v-if="cases.length > 0"
            :current-page="paginationState.cases.currentPage"
            :total-pages="paginationState.cases.totalPages"
            :total-items="paginationState.cases.totalItems"
            :page-size="paginationState.cases.pageSize"
            @page-change="handlePageChange('cases', $event)"
            @page-size-change="handlePageSizeChange('cases', $event)"
          />
        </div>

        <!-- Patients Table -->
        <div v-if="activeTab === 'patients'" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date of Birth</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gender</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Zip Code</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="patient in patients" :key="patient.id">
                <td class="px-6 py-4 whitespace-nowrap">
                  <router-link :to="{ name: 'patient-details', params: { patientId: patient.id } }" class="text-indigo-600 hover:text-indigo-900">
                    {{ patient.firstName }} {{ patient.middleName ? patient.middleName + ' ' : '' }}{{ patient.lastName }}
                  </router-link>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {{ formatDate(patient.dateOfBirth) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {{ patient.gender }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {{ patient.zipCode }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button class="text-indigo-600 hover:text-indigo-900 mr-3">
                    <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"
                    />
                    <path
                      fill-rule="evenodd"
                      d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  </button>
                  <button class="text-red-600 hover:text-red-900">
                    <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Pagination for Patients -->
          <Pagination
            v-if="patients.length > 0"
            :current-page="paginationState.patients.currentPage"
            :total-pages="paginationState.patients.totalPages"
            :total-items="paginationState.patients.totalItems"
            :page-size="paginationState.patients.pageSize"
            @page-change="handlePageChange('patients', $event)"
            @page-size-change="handlePageSizeChange('patients', $event)"
          />
        </div>

        <!-- Prescribers Table -->
        <div v-if="activeTab === 'prescribers'" class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fax</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="prescriber in prescribers" :key="prescriber.id">
                <td class="px-6 py-4 whitespace-nowrap">
                  <router-link :to="{ name: 'prescriber-details', params: { prescriberId: prescriber.id } }" class="text-indigo-600 hover:text-indigo-900">
                    {{ prescriber.firstName }} {{ prescriber.lastName }}
                  </router-link>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {{ prescriber.address }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  {{ prescriber.fax }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button class="text-indigo-600 hover:text-indigo-900 mr-3">
                    <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      d="M17.414 2.586a2 2 0 00-2.828 0L7 10.172V13h2.828l7.586-7.586a2 2 0 000-2.828z"
                    />
                    <path
                      fill-rule="evenodd"
                      d="M2 6a2 2 0 012-2h4a1 1 0 010 2H4v10h10v-4a1 1 0 112 0v4a2 2 0 01-2 2H4a2 2 0 01-2-2V6z"
                      clip-rule="evenodd"
                    />
                  </svg>
                  </button>
                  <button class="text-red-600 hover:text-red-900">                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fill-rule="evenodd"
                      d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                      clip-rule="evenodd"
                    />
                  </svg></button>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Pagination for Prescribers -->
          <Pagination
            v-if="prescribers.length > 0"
            :current-page="paginationState.prescribers.currentPage"
            :total-pages="paginationState.prescribers.totalPages"
            :total-items="paginationState.prescribers.totalItems"
            :page-size="paginationState.prescribers.pageSize"
            @page-change="handlePageChange('prescribers', $event)"
            @page-size-change="handlePageSizeChange('prescribers', $event)"
          />
        </div>

        <!-- Agent List -->
        <div v-if="activeTab === 'agents'" class="overflow-x-auto">
          <AgentList :show-auto-assign-modal="showAgentListAutoAssignModal" @close-auto-assign-modal="closeAgentListAutoAssignModal" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, reactive } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import Pagination from './utility/Pagination.vue';
import AgentList from './AgentList.vue';
import { useOrganization } from '../contexts/OrganizationContext';

interface Patient {
  id: string;
  firstName: string;
  middleName?: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  zipCode: string;
  age?: number;
  createdDate: string;
  updatedDate: string;
}

interface Prescriber {
  id: string;
  firstName: string;
  lastName: string;
  address: string;
  fax: string;
  createdDate: string;
  updatedDate: string;
}

interface Case {
  caseId: string;
  caseType: string;
  enrollmentStatus: string;
  patient: Patient;
  prescriber: Prescriber;
  drugFamily: {
    name: string;
  };
  createdDate: string;
}

interface PaginationMeta {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

interface PaginatedCaseResponse {
  data: Case[];
  meta: PaginationMeta;
}

interface PaginatedPatientResponse {
  data: Patient[];
  meta: PaginationMeta;
}

interface PaginatedPrescriberResponse {
  data: Prescriber[];
  meta: PaginationMeta;
}

interface PaginatedAgentResponse {
  data: Agent[];
  meta: PaginationMeta;
}

interface Agent {
  id: string;
  name: string;
  organization: string;
  skills: string;
  // Add other properties of Agent if known
}

interface TabPaginationState {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  pageSize: number;
}

interface CasesFetchParams {
  page: number;
  pageSize: number;
  organizationId?: string;
  caseProgressStatus?: string;
  search?: string;
  patientName?: string;
  prescriberName?: string;
  drugFamilyName?: string;
}

const router = useRouter();
const activeTab = ref('cases');
const activeProgressStage = ref('new');
const showSearchForm = ref(false);
const showAgentListAutoAssignModal = ref(false);
const patients = ref<Patient[]>([]);
const prescribers = ref<Prescriber[]>([]);
const cases = ref<Case[]>([]);
const agents = ref<Agent[]>([]);

// Pagination state for each tab
const paginationState = reactive({
  cases: {
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 10
  },
  patients: {
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 10
  },
  prescribers: {
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 10
  },
  agents: {
    currentPage: 1,
    totalPages: 0,
    totalItems: 0,
    pageSize: 10
  }
});

const tabs = [
  { id: 'cases', name: 'Cases' },
  { id: 'patients', name: 'Patients' },
  { id: 'prescribers', name: 'Prescribers' },
  { id: 'agents', name: 'Agents' },
];

// Search form data for different tabs
const caseSearchFormData = reactive({
  search: '',
  patientName: '',
  prescriberName: '',
  drugFamilyName: ''
});

const patientSearchFormData = reactive({
  firstName: '',
  lastName: '',
  dateOfBirth: '',
  gender: '',
  zipCode: ''
});

const prescriberSearchFormData = reactive({
  firstName: '',
  lastName: '',
  address: '',
  fax: ''
});

// Search form labels
const caseSearchFormLabels = {
  search: 'General Search',
  patientName: 'Patient Name',
  prescriberName: 'Prescriber Name',
  drugFamilyName: 'Drug Family Name'
};

const patientSearchFormLabels = {
  firstName: 'First Name',
  lastName: 'Last Name',
  dateOfBirth: 'Date of Birth',
  gender: 'Gender',
  zipCode: 'ZIP Code'
};

const prescriberSearchFormLabels = {
  firstName: 'First Name',
  lastName: 'Last Name',
  address: 'Address',
  fax: 'Fax'
};

const agentSearchFormLabels = {
  name: 'Name',
  organization: 'Organization',
  skills: 'Skills'
};

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString();
};

const deleteCase = async (caseId: string) => {
  // Show confirmation dialog
  const confirmed = confirm(`Are you sure you want to delete case ${caseId}? This action cannot be undone.`);

  if (!confirmed) {
    return;
  }

  try {
    // Call the delete API endpoint
    await axios.delete(`http://localhost:3000/api/patient-cases/${caseId}`);

    // Show success message
    alert('Case deleted successfully');

    // Refresh the cases list to reflect the deletion
    if (activeTab.value === 'cases') {
      fetchCasesByStage();
    }
  } catch (error: any) {
    console.error('Error deleting case:', error);

    // Show error message
    if (error.response?.status === 404) {
      alert('Case not found. It may have already been deleted.');
    } else {
      alert('Failed to delete case. Please try again.');
    }
  }
};

// Search toggle function
const toggleSearchForm = () => {
  showSearchForm.value = !showSearchForm.value;
};

// Case search functions
const submitCaseSearch = async () => {
  try {
    const orgId = getOrganizationId();
    const params: CasesFetchParams = {
      page: paginationState.cases.currentPage,
      pageSize: paginationState.cases.pageSize,
      ...caseSearchFormData,
      ...(orgId && { organizationId: orgId })
    };
    const response = await axios.get('http://localhost:3000/api/patient-cases', {
      params
    });
    cases.value = response.data.data;
    paginationState.cases.totalItems = response.data.meta.total;
    paginationState.cases.totalPages = response.data.meta.totalPages;
    activeTab.value = 'cases'; // Ensure cases tab is active after search
  } catch (error) {
    console.error('Error searching cases:', error);
  }
};

const clearCaseSearch = () => {
  Object.keys(caseSearchFormData).forEach(key => {
    (caseSearchFormData as any)[key] = '';
  });
  fetchCasesByStage(); // Reload without search filters
};

// Patient search functions
const submitPatientSearch = async () => {
  try {
    const orgId = getOrganizationId();
    const params = {
      ...patientSearchFormData,
      ...(orgId && { organizationId: orgId })
    };
    const response = await axios.get('http://localhost:3000/api/patient', {
      params
    });
    patients.value = response.data.data;
    paginationState.patients.totalItems = response.data.meta.total;
    paginationState.patients.totalPages = response.data.meta.totalPages;
    activeTab.value = 'patients';
  } catch (error) {
    console.error('Error searching patients:', error);
  }
};

const clearPatientSearch = () => {
  Object.keys(patientSearchFormData).forEach(key => {
    (patientSearchFormData as any)[key] = '';
  });
  fetchPatients(); // Reload without search filters
};

// Prescriber search functions
const submitPrescriberSearch = async () => {
  try {
    const orgId = getOrganizationId();
    const params = {
      ...prescriberSearchFormData,
      ...(orgId && { organizationId: orgId })
    };
    const response = await axios.get('http://localhost:3000/api/prescriber', {
      params
    });
    prescribers.value = response.data.data;
    paginationState.prescribers.totalItems = response.data.meta.total;
    paginationState.prescribers.totalPages = response.data.meta.totalPages;
    activeTab.value = 'prescribers';
  } catch (error) {
    console.error('Error searching prescribers:', error);
  }
};

const clearPrescriberSearch = () => {
  Object.keys(prescriberSearchFormData).forEach(key => {
    (prescriberSearchFormData as any)[key] = '';
  });
  fetchPrescribers(); // Reload without search filters
};

const createNewCase = () => {
  router.push('/case/new');
};

const goToOcrImport = () => {
  router.push('/ocr-import');
};

const createNewPatient = () => {
  router.push('/patient/new');
};

const createNewPrescriber = () => {
  router.push('/prescriber/new');
};

const fetchPatients = async (page = 1, pageSize = 10) => {
  try {
    paginationState.patients.currentPage = page;
    paginationState.patients.pageSize = pageSize;
    const orgId = getOrganizationId();
    const response = await axios.get('http://localhost:3000/api/patient', {
      params: {
        page,
        pageSize,
        ...(orgId && { organizationId: orgId })
      }
    });
    patients.value = response.data.data;
    paginationState.patients.totalItems = response.data.meta.total;
    paginationState.patients.totalPages = response.data.meta.totalPages;
  } catch (error) {
    console.error('Error fetching patients:', error);
    patients.value = [];
  }
};

const fetchPrescribers = async (page = 1, pageSize = 10) => {
  try {
    paginationState.prescribers.currentPage = page;
    paginationState.prescribers.pageSize = pageSize;
    const orgId = getOrganizationId();
    const response = await axios.get('http://localhost:3000/api/prescriber', {
      params: {
        page,
        pageSize,
        ...(orgId && { organizationId: orgId })
      }
    });
    prescribers.value = response.data.data;
    paginationState.prescribers.totalItems = response.data.meta.total;
    paginationState.prescribers.totalPages = response.data.meta.totalPages;
  } catch (error) {
    console.error('Error fetching prescribers:', error);
    prescribers.value = [];
  }
};

const fetchCasesByStage = async (page = 1, pageSize = 10) => {
  try {
    paginationState.cases.currentPage = page;
    paginationState.cases.pageSize = pageSize;
    const orgId = getOrganizationId();
    
    console.log('Fetching cases for organization:', orgId); // Debug log

    const params: CasesFetchParams = {
      page,
      pageSize,
      ...(orgId && { organizationId: orgId })
    };
    
    // Only add caseProgressStatus if not 'ALL'
    if (activeProgressStage.value !== 'ALL') {
      params.caseProgressStatus = activeProgressStage.value;
    }
    
    const response = await axios.get('http://localhost:3000/api/patient-cases', { params });
    cases.value = response.data.data;
    paginationState.cases.totalItems = response.data.meta.total;
    paginationState.cases.totalPages = response.data.meta.totalPages;
  } catch (error) {
    console.error('Error fetching cases:', error);
    cases.value = [];
  }
};

const progressStages = [
  { id: 'ALL', name: 'All Cases' },
  { id: 'STAGE_1', name: 'Intake' },
  { id: 'STAGE_2', name: 'BI Coverage' },
  { id: 'STAGE_3', name: 'RX Transfer' },
  { id: 'COMPLETED', name: 'Closed' },
  { id: 'ON_HOLD', name: 'On Hold' },
  { id: 'CANCELLED', name: 'Cancelled' }
];

// Pagination handlers
const handlePageChange = (tab: string, page: number) => {
  if (tab === 'cases') {
    paginationState.cases.currentPage = page;
    fetchCasesByStage();
  } else if (tab === 'patients') {
    paginationState.patients.currentPage = page;
    fetchPatients();
  } else if (tab === 'prescribers') {
    paginationState.prescribers.currentPage = page;
    fetchPrescribers();
  }
};

const handlePageSizeChange = (tab: string, pageSize: number) => {
  if (tab === 'cases') {
    paginationState.cases.pageSize = pageSize;
    paginationState.cases.currentPage = 1; // Reset to first page
    fetchCasesByStage();
  } else if (tab === 'patients') {
    paginationState.patients.pageSize = pageSize;
    paginationState.patients.currentPage = 1; // Reset to first page
    fetchPatients();
  } else if (tab === 'prescribers') {
    paginationState.prescribers.pageSize = pageSize;
    paginationState.prescribers.currentPage = 1; // Reset to first page
    fetchPrescribers();
  }
};

// Watch for tab changes
watch(activeTab, (newTab) => {
  // Close search form when switching tabs
  showSearchForm.value = false;

  switch (newTab) {
    case 'cases':
      fetchCasesByStage(); // Use the new function instead of fetchCases
      break;
    case 'patients':
      fetchPatients();
      break;
    case 'prescribers':
      fetchPrescribers();
      break;
    case 'agents':
      fetchAgents();
      break;
  }
});

watch(activeProgressStage, () => {
  if (activeTab.value === 'cases') {
    paginationState.cases.currentPage = 1; // Reset to first page when changing stage
    fetchCasesByStage();
  }
});

// Add agent-related methods
const createNewAgent = () => {
  router.push('/agents/create');
};

const goToAgents = () => {
  router.push('/agents');
};

const openAgentListAutoAssignModal = () => {
  showAgentListAutoAssignModal.value = true;
};

const closeAgentListAutoAssignModal = () => {
  showAgentListAutoAssignModal.value = false;
};

const manualAssignAgent = () => {
  alert('Manual Assign Agent functionality coming soon!');
};

// Add agent search form data
const agentSearchFormData = reactive({
  name: '',
  organization: '',
  skills: '',
});

// Add agent search methods
const submitAgentSearch = async () => {
  try {
    const orgId = getOrganizationId();
    const response = await axios.get('/api/agents', {
      params: {
        name: agentSearchFormData.name,
        organization: agentSearchFormData.organization,
        skills: agentSearchFormData.skills,
        ...(orgId && { organizationId: orgId })
      },
    });
    // Navigate to agents page with search results
    router.push({
      path: '/agents',
      query: {
        name: agentSearchFormData.name,
        organization: agentSearchFormData.organization,
        skills: agentSearchFormData.skills,
        ...(orgId && { organizationId: orgId })
      },
    });
  } catch (error) {
    console.error('Error searching agents:', error);
  }
};

const clearAgentSearch = () => {
  agentSearchFormData.name = '';
  agentSearchFormData.organization = '';
  agentSearchFormData.skills = '';
};

const { getOrganization, getOrganizationId, setOrganization } = useOrganization();

// Watch for changes in the current organization and refetch data
watch(getOrganization, () => {
  console.log('Organization changed, refetching data for:', getOrganizationId()); // Debug log
  switch (activeTab.value) {
    case 'cases':
      fetchCasesByStage();
      break;
    case 'patients':
      fetchPatients();
      break;
    case 'prescribers':
      fetchPrescribers();
      break;
    case 'agents':
      fetchAgents();
      break;
  }
});

const fetchAgents = async (page = 1, pageSize = 10) => {
  try {
    paginationState.agents.currentPage = page;
    paginationState.agents.pageSize = pageSize;
    const orgId = getOrganizationId();
    const response = await axios.get('/api/agents', {
      params: {
        page,
        pageSize,
        ...(orgId && { organizationId: orgId })
      }
    });
    agents.value = response.data.data;
    paginationState.agents.totalItems = response.data.meta.total;
    paginationState.agents.totalPages = response.data.meta.totalPages;
  } catch (error) {
    console.error('Error fetching agents:', error);
    agents.value = [];
  }
};

// Initialize active tab from URL
onMounted(() => {
  const urlParams = new URLSearchParams(window.location.search);
  const tabParam = urlParams.get('tab');
  if (tabParam && ['cases', 'patients', 'prescribers', 'agents'].includes(tabParam)) {
    activeTab.value = tabParam;
    if (tabParam === 'cases') {
      activeProgressStage.value = 'ALL'; // Set to 'All Cases' when the cases tab is selected
    }
  }

  const token = localStorage.getItem('token');
  if (!token) {
    router.push('/login');
    return;
  }

  // Check if there's a selected organization in localStorage
  const storedOrg = localStorage.getItem('selectedOrganization');
  if (storedOrg) {
    const organization = JSON.parse(storedOrg);
    setOrganization(organization); // Ensure setOrganization is correctly called here
  }

  // Initial data fetch based on the active tab
  switch (activeTab.value) {
    case 'cases':
      fetchCasesByStage();
      break;
    case 'patients':
      fetchPatients();
      break;
    case 'prescribers':
      fetchPrescribers();
      break;
    case 'agents':
      // No initial fetch for agents, as they are usually searched or auto-assigned
      break;
  }
});
</script>

<style scoped>
/* Styles are handled by Tailwind classes */
</style>
