<template>
  <div class="bg-white shadow rounded-lg">
    <!-- Header -->
    <div class="px-6 py-4 border-b border-gray-200">
      <div class="flex justify-between items-center">
        <h3 class="text-lg font-medium text-gray-900">
          {{ showAutoAssignModal ? 'Select Agent for Auto Assignment' : 'Agents' }}
        </h3>
        <div class="flex space-x-2" v-if="!showAutoAssignModal">
          <button
            @click="createNewAgent"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
          >
            Create New Agent
          </button>
          <button
            @click="toggleAutoAssign"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
          >
            Auto Assign Agent
          </button>
          <button
            @click="manualAssignAgent"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700"
          >
            Manual Assign Agent
          </button>
          <button
            @click="viewAllAgents"
            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
          >
            View All Agents
          </button>
        </div>
      </div>
    </div>

    <!-- Agents Table -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Agent ID</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Organization</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Role</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Address</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">AI Score</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Completed Cases</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Skills</th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="agent in agents" :key="agent.id">
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.id }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <router-link :to="{ name: 'agent-details', params: { id: agent.id } }" class="text-indigo-600 hover:text-indigo-900">
                {{ getAgentName(agent) }}
              </router-link>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ getOrganizationName(agent) }}</td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="getRoleBadgeClass(agent.role)" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                {{ agent.role || 'agent' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.address || 'N/A' }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.aiClassifiedScore ? agent.aiClassifiedScore.toFixed(2) : '0.00' }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ getCompletedCases(agent) }}</td>
            <td class="px-6 py-4 whitespace-nowrap">{{ agent.completedSkills?.join(', ') || 'None' }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button
                  @click="editAgent(agent)"
                  class="text-indigo-600 hover:text-indigo-900"
                  title="Edit Agent"
                >
                  <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </button>
                <button
                  @click="deleteAgent(agent)"
                  class="text-red-600 hover:text-red-900"
                  title="Delete Agent"
                >
                  <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- Pagination -->
    <div class="px-6 py-4 border-t border-gray-200">
      <Pagination
        :current-page="paginationState.currentPage"
        :total-pages="paginationState.totalPages"
        :total-items="paginationState.totalItems"
        :page-size="paginationState.pageSize"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      />
    </div>

    <!-- Close button for modal -->
    <div v-if="showAutoAssignModal" class="px-6 py-4 border-t border-gray-200">
      <button
        @click="closeAutoAssignModal"
        class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
      >
        Close
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, defineProps, defineEmits } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import Pagination from './utility/Pagination.vue';

interface Agent {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: 'admin' | 'agent';
  organization_id: string;
  organization?: {
    id: string;
    name: string;
  };
  address?: string;
  aiClassifiedScore: number;
  completedSkills: string[];
  caseConnections?: any[];
}

const props = defineProps<{ showAutoAssignModal: boolean }>();
const emit = defineEmits(['close-auto-assign-modal']);

const router = useRouter();
const agents = ref<Agent[]>([]);

const paginationState = reactive({
  currentPage: 1,
  totalPages: 0,
  totalItems: 0,
  pageSize: 10
});

const getAgentName = (agent: Agent): string => {
  return `${agent.firstName} ${agent.lastName}`;
};

const getOrganizationName = (agent: Agent): string => {
  return agent.organization?.name || agent.organization_id || 'N/A';
};

const getRoleBadgeClass = (role: string): string => {
  return role === 'admin' 
    ? 'bg-purple-100 text-purple-800'
    : 'bg-blue-100 text-blue-800';
};

const getCompletedCases = (agent: Agent): number => {
  return agent.caseConnections?.filter(conn => conn.status === 'completed').length || 0;
};

const fetchAgents = async () => {
  try {
    const response = await axios.get('/api/agents', {
      params: {
        page: paginationState.currentPage,
        limit: paginationState.pageSize,
      }
    });

    agents.value = response.data.data;
    paginationState.totalItems = response.data.meta.total;
    paginationState.totalPages = response.data.meta.totalPages;
  } catch (error) {
    console.error('Error fetching agents:', error);
  }
};

const handlePageChange = (page: number) => {
  paginationState.currentPage = page;
  fetchAgents();
};

const handlePageSizeChange = (size: number) => {
  paginationState.pageSize = size;
  paginationState.currentPage = 1;
  fetchAgents();
};

const createNewAgent = () => {
  router.push('/agents/create');
};

const editAgent = (agent: Agent) => {
  router.push(`/agents/${agent.id}`);
};

const deleteAgent = async (agent: Agent) => {
  if (confirm(`Are you sure you want to delete ${getAgentName(agent)}?`)) {
    try {
      await axios.delete(`/api/agents/${agent.id}`);
      fetchAgents();
    } catch (error) {
      console.error('Error deleting agent:', error);
    }
  }
};

const toggleAutoAssign = () => {
  // Auto assign logic
};

const manualAssignAgent = () => {
  // Manual assign logic
};

const viewAllAgents = () => {
  router.push('/agents');
};

const closeAutoAssignModal = () => {
  emit('close-auto-assign-modal');
};

onMounted(() => {
  fetchAgents();
});
</script>
