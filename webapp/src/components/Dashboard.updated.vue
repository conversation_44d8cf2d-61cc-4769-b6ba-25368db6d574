<template>
  <div class="min-h-screen bg-gray-100">
    <!-- Navigation Bar -->
    <nav class="bg-white shadow-lg">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between h-16">
          <div class="flex">
            <!-- Logo/Brand -->
            <div class="flex-shrink-0 flex items-center">
              <h1 class="text-xl font-bold text-gray-800">PSHUB Dashboard</h1>
            </div>

            <!-- Navigation Links -->
            <div class="hidden sm:ml-6 sm:flex sm:space-x-8">
              <button
                v-for="tab in availableTabs"
                :key="tab.id"
                @click="activeTab = tab.id"
                :class="[
                  activeTab === tab.id
                    ? 'border-indigo-500 text-gray-900'
                    : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700',
                  'inline-flex items-center px-1 pt-1 border-b-2 text-sm font-medium'
                ]"
              >
                {{ tab.name }}
              </button>
            </div>
          </div>

          <!-- User Info -->
          <div class="flex items-center space-x-4">
            <span class="text-sm text-gray-700">
              {{ currentUser?.firstName }} {{ currentUser?.lastName }}
              <span :class="getRoleBadgeClass(currentUser?.role)" class="ml-2 px-2 py-1 text-xs font-semibold rounded-full">
                {{ currentUser?.role || 'agent' }}
              </span>
            </span>
            <button
              @click="logout"
              class="text-gray-500 hover:text-gray-700"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
      <!-- Cases Tab -->
      <div v-if="activeTab === 'cases'">
        <!-- Cases content here -->
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Cases</h2>
          <!-- Add your existing cases component here -->
        </div>
      </div>

      <!-- Patients Tab -->
      <div v-if="activeTab === 'patients'">
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Patients</h2>
          <!-- Add your existing patients component here -->
        </div>
      </div>

      <!-- Prescribers Tab -->
      <div v-if="activeTab === 'prescribers'">
        <div class="bg-white shadow rounded-lg p-6">
          <h2 class="text-lg font-medium text-gray-900 mb-4">Prescribers</h2>
          <!-- Add your existing prescribers component here -->
        </div>
      </div>

      <!-- Agents Tab -->
      <div v-if="activeTab === 'agents'">
        <AgentList :show-auto-assign-modal="false" />
      </div>

      <!-- Admins Tab (Admin only) -->
      <div v-if="activeTab === 'admins' && isAdmin">
        <AdminList />
      </div>

      <!-- Organizations Tab (Admin only) -->
      <div v-if="activeTab === 'organizations' && isAdmin">
        <OrganizationManagement />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import AgentList from './AgentList.updated.vue';
import AdminList from './AdminList.vue';
import OrganizationManagement from './OrganizationManagement.vue';

interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: 'admin' | 'agent';
  organization_id: string;
}

const router = useRouter();
const activeTab = ref('cases');
const currentUser = ref<User | null>(null);

const allTabs = [
  { id: 'cases', name: 'Cases' },
  { id: 'patients', name: 'Patients' },
  { id: 'prescribers', name: 'Prescribers' },
  { id: 'agents', name: 'Agents' },
  { id: 'admins', name: 'Admins' },
  { id: 'organizations', name: 'Organizations' }
];

const isAdmin = computed(() => currentUser.value?.role === 'admin');

const availableTabs = computed(() => {
  if (isAdmin.value) {
    return allTabs; // Admins see all tabs
  } else {
    // Agents see limited tabs
    return allTabs.filter(tab => !['admins', 'organizations'].includes(tab.id));
  }
});

const getRoleBadgeClass = (role: string): string => {
  return role === 'admin' 
    ? 'bg-purple-100 text-purple-800'
    : 'bg-blue-100 text-blue-800';
};

const fetchCurrentUser = async () => {
  try {
    const response = await axios.get('/api/auth/me');
    currentUser.value = response.data;
  } catch (error) {
    console.error('Error fetching current user:', error);
    // Redirect to login if not authenticated
    router.push('/login');
  }
};

const logout = () => {
  localStorage.removeItem('token');
  localStorage.removeItem('currentOrganizationId');
  router.push('/login');
};

onMounted(() => {
  fetchCurrentUser();
  
  // Initialize active tab from URL
  const urlParams = new URLSearchParams(window.location.search);
  const tabParam = urlParams.get('tab');
  if (tabParam && availableTabs.value.some(tab => tab.id === tabParam)) {
    activeTab.value = tabParam;
  }
});
</script>
