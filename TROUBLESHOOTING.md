# PSHUB Application Troubleshooting Guide

## Overview
This guide helps resolve common issues with the PSHUB application after the React to Vue.js conversion.

## Quick Fix Commands

### 1. Clean Git Status
```bash
cd /Users/<USER>/hat/PSHUB
git add .
git reset --hard HEAD
```

### 2. Install Dependencies
```bash
# Frontend
cd webapp
npm install

# Backend
cd ../nestbe
yarn install
```

### 3. Start Development Servers
```bash
# Terminal 1 - Backend
cd nestbe
yarn start:dev

# Terminal 2 - Frontend
cd webapp
npm run dev
```

## Common Issues and Solutions

### Issue 1: Module Not Found Errors
**Problem**: Import errors for React components or missing modules
**Solution**: 
- All components are now Vue.js (.vue files)
- Check import paths in router and components
- Ensure all dependencies are installed

### Issue 2: Compilation Errors
**Problem**: TypeScript or build errors
**Solution**:
```bash
# Check frontend compilation
cd webapp
npm run build

# Check backend compilation
cd nestbe
yarn build
```

### Issue 3: Database Connection Issues
**Problem**: Backend fails to connect to PostgreSQL
**Solution**:
```bash
# Start PostgreSQL
sudo service postgresql start

# Test connection
psql -h localhost -U hubsolution -d hubsolution

# Run migrations
cd nestbe
yarn typeorm migration:run
```

### Issue 4: Port Conflicts
**Problem**: Ports 3000 or 5173 already in use
**Solution**:
```bash
# Kill processes on port 3000
lsof -ti:3000 | xargs kill -9

# Kill processes on port 5173
lsof -ti:5173 | xargs kill -9
```

### Issue 5: Environment Configuration
**Problem**: Missing environment variables
**Solution**: Create `.env` file in nestbe directory:
```env
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=hubsolution
DB_PASSWORD=9080
DB_DATABASE=hubsolution
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h
NODE_ENV=development
PORT=3000
```

## Application Architecture

### Frontend (Vue.js)
- **Framework**: Vue 3 + TypeScript + Vite
- **Port**: 5173
- **Main Components**:
  - Dashboard.vue
  - CaseDetails.vue
  - PatientDetails.vue
  - PrescriberDetails.vue

### Backend (NestJS)
- **Framework**: NestJS + TypeORM + PostgreSQL
- **Port**: 3000
- **API Prefix**: /api
- **Documentation**: http://localhost:3000/api

## Key Features to Test

### 1. Patient Management
- ✅ Create/Edit/View patients
- ✅ Real-time age calculation from DOB
- ✅ Insurance management
- ✅ Search functionality

### 2. Case Management
- ✅ 4-stage progression (STAGE_1 → STAGE_2 → STAGE_3 → COMPLETED)
- ✅ Case validation before stage progression
- ✅ Real-time updates without page reload
- ✅ Accordion-style forms

### 3. Prescriber Management
- ✅ Create/Edit/View prescribers
- ✅ Associate with cases
- ✅ Search functionality

### 4. Drug Family & Products
- ✅ Dynamic form handling
- ✅ Real-time updates

### 5. Coverage & Consent
- ✅ BI coverage validation
- ✅ Consent management
- ✅ Attachment handling

### 6. Search & Pagination
- ✅ Real-time search across all sections
- ✅ 10 records per page pagination
- ✅ Maintains context during filtering

## Validation Rules

### Stage Progression
- **STAGE_1 → STAGE_2**: All sections except coverage must be filled
- **STAGE_2 → STAGE_3**: Coverage details must be validated
- **Error Display**: Grouped by section, persistent messages

### Form Validation
- **Patient**: Required fields, age calculation
- **Prescriber**: Required contact information
- **Drug Family**: Name and description required
- **Coverage**: Insurance validation for stage progression

## API Endpoints

### Core Endpoints
- `GET /api/patient-cases` - List cases with pagination
- `POST /api/patient-cases` - Create new case
- `GET /api/patient-cases/:id` - Get case details
- `PATCH /api/patient-cases/:id` - Update case
- `POST /api/patient-cases/ocr-import` - OCR import

### Search Endpoints
- `GET /api/patient-cases?search=term` - Search cases
- `GET /api/patient?search=term` - Search patients
- `GET /api/prescriber?search=term` - Search prescribers

## Development Workflow

1. **Start Backend**: `cd nestbe && yarn start:dev`
2. **Start Frontend**: `cd webapp && npm run dev`
3. **Access Application**: http://localhost:5173
4. **API Documentation**: http://localhost:3000/api
5. **Database**: PostgreSQL on localhost:5432

## Testing Checklist

- [ ] Both servers start without errors
- [ ] Database connection successful
- [ ] Login/authentication works
- [ ] Dashboard loads with data
- [ ] Case creation and editing
- [ ] Patient management
- [ ] Prescriber management
- [ ] Search functionality
- [ ] Pagination works
- [ ] Stage progression
- [ ] Form validations
- [ ] Real-time updates
