#!/bin/bash

echo "🔧 Applying final compilation fixes..."

cd /Users/<USER>/hat/PSHUB/nestbe/src

# Remove problematic files
echo "🗑️ Removing problematic files..."
rm -f agent/agent.controller.ts
rm -f agent/agent.service.ts
rm -f organization/organization.service.ts
rm -f organization/organization.controller.ts

# Copy final corrected versions
echo "📁 Copying final corrected files..."

# Agent files
cp agent/agent.controller.final.ts agent/agent.controller.ts
cp agent/agent.service.final.ts agent/agent.service.ts

# Organization files
cp organization/organization.service.final.ts organization/organization.service.ts
cp organization/organization.controller.final.ts organization/organization.controller.ts

echo "✅ All final fixes applied!"
echo ""
echo "🔨 Now run: cd /Users/<USER>/hat/PSHUB/nestbe && yarn build"
