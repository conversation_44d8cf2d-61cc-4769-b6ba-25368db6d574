#!/bin/bash

echo "🧹 Final Cleanup - Removing ALL development files..."
echo "=================================================="

cd /Users/<USER>/hat/PSHUB

# Function to find and remove files with specific patterns
cleanup_files() {
    local pattern=$1
    local description=$2
    echo "🔍 Finding $description files..."
    
    # Find and list files first
    find . -name "$pattern" -type f | while read file; do
        echo "  Removing: $file"
        rm -f "$file"
    done
}

# Remove all .updated files
cleanup_files "*.updated.*" ".updated"

# Remove all .fixed files  
cleanup_files "*.fixed.*" ".fixed"

# Remove all .bak files
cleanup_files "*.bak" ".bak"

# Remove all .spec.ts files (test files)
cleanup_files "*.spec.ts" ".spec.ts (test)"

# Remove specific problematic files
echo "🎯 Removing specific problematic files..."

# Backend specific files
rm -f nestbe/src/auth/strategies/jwt.strategy.fixed.ts
rm -f nestbe/src/organization/organization.service.updated.ts

# Frontend specific files
rm -f webapp/src/components/Dashboard.updated.vue
rm -f webapp/src/components/AgentList.updated.vue

# Remove root level development files
echo "📁 Removing root level development files..."
rm -f QUICK_FIX_GUIDE.md
rm -f ROLE_BASED_ACCESS_IMPLEMENTATION.md
rm -f TROUBLESHOOTING.md
rm -f apply-final-fixes.sh
rm -f apply-fixes.sh
rm -f check-servers.sh
rm -f complete-fix.sh
rm -f emergency-fix.sh
rm -f final-fix.md
rm -f fix-application.sh
rm -f fix-compilation.sh
rm -f fix-jwt-issue.sh
rm -f implement-role-based-access.sh
rm -f quick-fix-compilation.sh
rm -f run-migration.sh
rm -f start-both.sh
rm -f start-dev.sh
rm -f cleanup-dev-files.sh

# Clean build artifacts
echo "📁 Cleaning build artifacts..."
rm -rf nestbe/dist
rm -rf webapp/dist

# Remove this cleanup script itself
echo "🗑️  Removing cleanup scripts..."
rm -f final-cleanup.sh

echo ""
echo "✅ Final cleanup complete!"
echo "========================"
echo ""
echo "🎯 All development files removed:"
echo "   ✅ .updated files"
echo "   ✅ .fixed files" 
echo "   ✅ .bak files"
echo "   ✅ .spec.ts files"
echo "   ✅ Development scripts"
echo "   ✅ Build artifacts"
echo ""
echo "🚀 Project is now clean and ready for production!"
echo ""
echo "📝 To start development:"
echo "   1. Backend:  cd nestbe && yarn start:dev"
echo "   2. Frontend: cd webapp && npm run dev"
echo "   3. Login:    <EMAIL> / admin123"
