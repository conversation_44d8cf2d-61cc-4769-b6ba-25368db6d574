#!/bin/bash

# Quick fix for compilation errors

echo "🔧 Fixing compilation errors..."

cd /Users/<USER>/hat/PSHUB/nestbe/src/agent

# Copy updated files to replace originals
echo "📁 Replacing agent entity..."
cp agent.entity.updated.ts agent.entity.ts

echo "📁 Replacing agent service..."
cp agent.service.updated.ts agent.service.ts

echo "📁 Replacing agent controller..."
cp agent.controller.updated.ts agent.controller.ts

echo "📁 Replacing DTOs..."
cp dto/create-agent.dto.updated.ts dto/create-agent.dto.ts
cp dto/update-agent.dto.updated.ts dto/update-agent.dto.ts

# Fix organization service
cd ../organization
echo "📁 Replacing organization service..."
cp organization.service.updated.ts organization.service.ts
cp organization.controller.updated.ts organization.controller.ts

# Now let's fix the specific compilation issues
cd ../agent

# Fix the agent service issues
echo "🔧 Fixing agent service compilation issues..."

# Create a corrected agent service
cat > agent.service.ts << 'EOF'
import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Agent, UserRole } from './agent.entity';
import { AgentCaseConnection } from './agent-case-connection.entity';
import { PatientCase } from '../patient-case/entities/patient-case.entity';
import { Organization } from '../organization/entities/organization.entity';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';

interface AgentSearchParams {
  name?: string;
  organization?: string;
  skills?: string;
  role?: UserRole;
  page?: number;
  limit?: number;
}

interface PaginatedAgentResponse {
  data: Agent[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

@Injectable()
export class AgentService {
  constructor(
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
    @InjectRepository(AgentCaseConnection)
    private connectionRepository: Repository<AgentCaseConnection>,
    @InjectRepository(PatientCase)
    private patientCaseRepository: Repository<PatientCase>,
  ) {}

  async searchAgents(params: AgentSearchParams, organization?: Organization): Promise<PaginatedAgentResponse> {
    const queryBuilder = this.agentRepository.createQueryBuilder('agent')
      .leftJoinAndSelect('agent.organization', 'organization')
      .leftJoinAndSelect('agent.caseConnections', 'caseConnections');

    // Organization filtering
    if (organization) {
      queryBuilder.andWhere('agent.organization_id = :organizationId', { 
        organizationId: organization.id 
      });
    }

    // Search filters
    if (params.name) {
      queryBuilder.andWhere(
        '(agent.firstName ILIKE :name OR agent.lastName ILIKE :name)',
        { name: `%${params.name}%` }
      );
    }

    if (params.skills) {
      queryBuilder.andWhere('agent.completedSkills::text ILIKE :skills', { 
        skills: `%${params.skills}%` 
      });
    }

    if (params.role) {
      queryBuilder.andWhere('agent.role = :role', { role: params.role });
    }

    // Pagination
    const page = params.page || 1;
    const limit = params.limit || 10;
    const skip = (page - 1) * limit;

    queryBuilder.skip(skip).take(limit);

    const [agents, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data: agents,
      meta: {
        total,
        page,
        limit,
        totalPages,
      },
    };
  }

  async getAllAgents(page = 1, limit = 10, organization?: Organization): Promise<PaginatedAgentResponse> {
    const queryBuilder = this.agentRepository.createQueryBuilder('agent')
      .leftJoinAndSelect('agent.organization', 'organization')
      .leftJoinAndSelect('agent.caseConnections', 'caseConnections');

    if (organization) {
      queryBuilder.andWhere('agent.organization_id = :organizationId', { 
        organizationId: organization.id 
      });
    }

    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [agents, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data: agents,
      meta: {
        total,
        page,
        limit,
        totalPages,
      },
    };
  }

  async getAgentsByRole(role: UserRole, organization?: Organization): Promise<Agent[]> {
    const queryBuilder = this.agentRepository.createQueryBuilder('agent')
      .leftJoinAndSelect('agent.organization', 'organization')
      .where('agent.role = :role', { role });

    if (organization) {
      queryBuilder.andWhere('agent.organization_id = :organizationId', { 
        organizationId: organization.id 
      });
    }

    return queryBuilder.getMany();
  }

  async getAllAdmins(): Promise<Agent[]> {
    return this.agentRepository.find({
      where: { role: UserRole.ADMIN },
      relations: ['organization'],
    });
  }

  async createAgent(createAgentDto: CreateAgentDto, organization?: Organization): Promise<Agent> {
    const agent = this.agentRepository.create({
      ...createAgentDto,
      organization_id: organization?.id || 'HUB-1',
    });

    return this.agentRepository.save(agent);
  }

  async updateAgent(
    id: number, 
    updateAgentDto: UpdateAgentDto, 
    currentUser?: Agent
  ): Promise<Agent> {
    const agent = await this.agentRepository.findOne({
      where: { id },
      relations: ['organization'],
    });

    if (!agent) {
      throw new NotFoundException(`Agent with ID ${id} not found`);
    }

    // Check permissions
    if (currentUser?.role === UserRole.AGENT && agent.organization_id !== currentUser.organization_id) {
      throw new ForbiddenException('You can only edit agents within your organization');
    }

    // Agents cannot change roles
    if (currentUser?.role === UserRole.AGENT && updateAgentDto.role) {
      delete updateAgentDto.role;
    }

    Object.assign(agent, updateAgentDto);
    return this.agentRepository.save(agent);
  }

  async getAgentDetails(agentId: number, currentUser?: Agent): Promise<Agent> {
    const agent = await this.agentRepository.findOne({
      where: { id: agentId },
      relations: ['organization', 'caseConnections'],
    });

    if (!agent) {
      throw new NotFoundException(`Agent with ID ${agentId} not found`);
    }

    // Check permissions for agents
    if (currentUser?.role === UserRole.AGENT && agent.organization_id !== currentUser.organization_id) {
      throw new ForbiddenException('You can only view agents within your organization');
    }

    return agent;
  }

  async assignAgentToCase(agentId: number, caseId: number): Promise<AgentCaseConnection> {
    const agent = await this.agentRepository.findOne({ where: { id: agentId } });
    const patientCase = await this.patientCaseRepository.findOne({ where: { caseId: caseId.toString() } });

    if (!agent || !patientCase) {
      throw new NotFoundException('Agent or Case not found');
    }

    const connection = this.connectionRepository.create({
      agentId,
      caseId,
      status: 'assigned',
    });

    return this.connectionRepository.save(connection);
  }

  async checkCaseAssignment(caseId: number) {
    const connection = await this.connectionRepository.findOne({
      where: { caseId },
      relations: ['agent'],
    });

    if (connection) {
      return {
        isAssigned: true,
        agentName: `${connection.agent.firstName} ${connection.agent.lastName}`,
        agentId: connection.agent.id,
        status: connection.status
      };
    }

    return {
      isAssigned: false
    };
  }

  async deleteAgent(agentId: number, currentUser?: Agent): Promise<void> {
    const agent = await this.agentRepository.findOne({ where: { id: agentId } });

    if (!agent) {
      throw new NotFoundException(`Agent with ID ${agentId} not found`);
    }

    // Check permissions
    if (currentUser?.role === UserRole.AGENT && agent.organization_id !== currentUser.organization_id) {
      throw new ForbiddenException('You can only delete agents within your organization');
    }

    await this.agentRepository.delete(agentId);
  }
}
EOF

# Fix the agent controller
echo "🔧 Fixing agent controller..."

cat > agent.controller.ts << 'EOF'
import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Body, 
  Param, 
  Query, 
  Delete, 
  Patch, 
  UseGuards,
  Request,
  ForbiddenException
} from '@nestjs/common';
import { AgentService } from './agent.service';
import { Agent, UserRole } from './agent.entity';
import { AgentCaseConnection } from './agent-case-connection.entity';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { OrganizationDecorator } from '../common/decorators/organization.decorator';
import { Organization } from '../organization/entities/organization.entity';

interface PaginatedAgentResponse {
  data: Agent[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

@Controller('agents')
@UseGuards(JwtAuthGuard)
export class AgentController {
  constructor(private readonly agentService: AgentService) {}

  @Get()
  async getAllAgents(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('role') role?: UserRole,
    @Query('name') name?: string,
    @Query('skills') skills?: string,
    @Request() req: any,
    @OrganizationDecorator() organization?: Organization,
  ): Promise<PaginatedAgentResponse> {
    const currentUser = req.user as Agent;
    
    // If user is an agent, only show agents from their organization
    const filterOrganization = currentUser.role === UserRole.AGENT ? 
      { id: currentUser.organization_id } as Organization : 
      organization;

    if (name || skills || role) {
      return this.agentService.searchAgents({
        page,
        limit,
        name,
        skills,
        role,
      }, filterOrganization);
    }

    return this.agentService.getAllAgents(page, limit, filterOrganization);
  }

  @Get('admins')
  async getAllAdmins(@Request() req: any): Promise<Agent[]> {
    const currentUser = req.user as Agent;
    
    // Only admins can view admin list
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can view admin list');
    }

    return this.agentService.getAllAdmins();
  }

  @Get('by-role/:role')
  async getAgentsByRole(
    @Param('role') role: UserRole,
    @Request() req: any,
    @OrganizationDecorator() organization?: Organization,
  ): Promise<Agent[]> {
    const currentUser = req.user as Agent;
    
    const filterOrganization = currentUser.role === UserRole.AGENT ? 
      { id: currentUser.organization_id } as Organization : 
      organization;

    return this.agentService.getAgentsByRole(role, filterOrganization);
  }

  @Get(':id')
  async getAgentDetails(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<Agent> {
    const currentUser = req.user as Agent;
    return this.agentService.getAgentDetails(parseInt(id), currentUser);
  }

  @Post()
  async createAgent(
    @Body() createAgentDto: CreateAgentDto,
    @Request() req: any,
    @OrganizationDecorator() organization?: Organization,
  ): Promise<Agent> {
    const currentUser = req.user as Agent;
    
    // Only admins can create other admins
    if (createAgentDto.role === UserRole.ADMIN && currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can create admin users');
    }

    // Agents can only create agents in their organization
    const targetOrganization = currentUser.role === UserRole.AGENT ? 
      { id: currentUser.organization_id } as Organization : 
      organization;

    return this.agentService.createAgent(createAgentDto, targetOrganization);
  }

  @Patch(':id')
  async updateAgent(
    @Param('id') id: string,
    @Body() updateAgentDto: UpdateAgentDto,
    @Request() req: any,
  ): Promise<Agent> {
    const currentUser = req.user as Agent;
    return this.agentService.updateAgent(parseInt(id), updateAgentDto, currentUser);
  }

  @Post(':agentId/assign/:caseId')
  async assignAgentToCase(
    @Param('agentId') agentId: string,
    @Param('caseId') caseId: string,
  ): Promise<AgentCaseConnection> {
    return this.agentService.assignAgentToCase(
      parseInt(agentId),
      parseInt(caseId),
    );
  }

  @Get('case/:caseId/assignment')
  async checkCaseAssignment(@Param('caseId') caseId: string) {
    return this.agentService.checkCaseAssignment(parseInt(caseId));
  }

  @Delete(':id')
  async deleteAgent(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    const currentUser = req.user as Agent;
    return this.agentService.deleteAgent(parseInt(id), currentUser);
  }
}
EOF

# Fix DTOs
echo "🔧 Fixing DTOs..."

cat > dto/create-agent.dto.ts << 'EOF'
import { IsEmail, IsString, IsOptional, IsEnum, IsArray, IsNumber } from 'class-validator';
import { UserRole } from '../agent.entity';

export class CreateAgentDto {
  @IsEmail()
  email: string;

  @IsString()
  password: string;

  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @IsOptional()
  @IsNumber()
  aiClassifiedScore?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  completedSkills?: string[];

  @IsOptional()
  @IsNumber()
  completedDuration?: number;

  @IsOptional()
  @IsString()
  address?: string;
}
EOF

cat > dto/update-agent.dto.ts << 'EOF'
import { PartialType } from '@nestjs/mapped-types';
import { CreateAgentDto } from './create-agent.dto';
import { IsOptional, IsEnum } from 'class-validator';
import { UserRole } from '../agent.entity';

export class UpdateAgentDto extends PartialType(CreateAgentDto) {
  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;
}
EOF

# Fix organization service
cd ../organization

cat > organization.service.ts << 'EOF'
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Organization } from './entities/organization.entity';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';

interface PaginatedOrganizationResponse {
  data: Organization[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

@Injectable()
export class OrganizationService {
    private static instance: OrganizationService;

    constructor(
        @InjectRepository(Organization)
        private readonly organizationRepository: Repository<Organization>,
    ) {
        OrganizationService.instance = this;
    }

    static getInstance(): OrganizationService {
        return OrganizationService.instance;
    }

    async findAll(): Promise<{ data: Organization[] }> {
        const organizations = await this.organizationRepository.find();
        return { data: organizations };
    }

    async findAllPaginated(page = 1, limit = 10): Promise<PaginatedOrganizationResponse> {
        const skip = (page - 1) * limit;
        
        const [organizations, total] = await this.organizationRepository.findAndCount({
            skip,
            take: limit,
            order: { created_at: 'DESC' },
        });

        const totalPages = Math.ceil(total / limit);

        return {
            data: organizations,
            meta: {
                total,
                page,
                limit,
                totalPages,
            },
        };
    }

    async findOne(id: string): Promise<Organization> {
        const organization = await this.organizationRepository.findOne({ where: { id } });
        if (!organization) {
            throw new NotFoundException(`Organization with ID ${id} not found`);
        }
        return organization;
    }

    async create(createOrganizationDto: CreateOrganizationDto): Promise<Organization> {
        const organization = this.organizationRepository.create(createOrganizationDto);
        await this.generateId(organization);
        return this.organizationRepository.save(organization);
    }

    async update(id: string, updateOrganizationDto: UpdateOrganizationDto): Promise<Organization> {
        const organization = await this.findOne(id);
        Object.assign(organization, updateOrganizationDto);
        return this.organizationRepository.save(organization);
    }

    async remove(id: string): Promise<void> {
        const organization = await this.findOne(id);
        await this.organizationRepository.remove(organization);
    }

    async getDefaultOrganization(): Promise<Organization> {
        let defaultOrg = await this.organizationRepository.findOne({ 
            where: { id: 'HUB-1' } 
        });
        
        if (!defaultOrg) {
            // Create default organization with HUB-1 ID
            defaultOrg = this.organizationRepository.create({
                id: 'HUB-1',
                name: 'Default Organization',
            });
            defaultOrg = await this.organizationRepository.save(defaultOrg);
        }
        
        return defaultOrg;
    }

    private async generateId(organization: Organization): Promise<void> {
        const latestOrg = await this.organizationRepository.findOne({
            where: {},
            order: {
                id: 'DESC'
            }
        });
        const nextNumber = latestOrg ? parseInt(latestOrg.id.split('-')[1]) + 1 : 1;
        organization.id = `HUB-${nextNumber}`;
    }

    static async getLatestOrganization(): Promise<Organization | null> {
        const instance = OrganizationService.getInstance();
        if (!instance) return null;

        return instance.organizationRepository.findOne({
            where: {},
            order: {
                id: 'DESC'
            }
        });
    }
}
EOF

cat > organization.controller.ts << 'EOF'
import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Body, 
  Param, 
  Delete, 
  UseGuards,
  Request,
  ForbiddenException,
  Query
} from '@nestjs/common';
import { OrganizationService } from './organization.service';
import { Organization } from './entities/organization.entity';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Agent, UserRole } from '../agent/agent.entity';

interface PaginatedOrganizationResponse {
  data: Organization[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

@Controller('organizations')
@UseGuards(JwtAuthGuard)
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Get()
  async getAllOrganizations(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Request() req: any,
  ): Promise<PaginatedOrganizationResponse> {
    const currentUser = req.user as Agent;
    
    // Only admins can view all organizations
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can view all organizations');
    }

    return this.organizationService.findAllPaginated(page, limit);
  }

  @Get('default')
  async getDefaultOrganization(): Promise<Organization> {
    return this.organizationService.getDefaultOrganization();
  }

  @Get(':id')
  async getOrganization(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<Organization> {
    const currentUser = req.user as Agent;
    
    // Agents can only view their own organization
    if (currentUser.role === UserRole.AGENT && currentUser.organization_id !== id) {
      throw new ForbiddenException('You can only view your own organization');
    }

    return this.organizationService.findOne(id);
  }

  @Post()
  async createOrganization(
    @Body() createOrganizationDto: CreateOrganizationDto,
    @Request() req: any,
  ): Promise<Organization> {
    const currentUser = req.user as Agent;
    
    // Only admins can create organizations
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can create organizations');
    }

    return this.organizationService.create(createOrganizationDto);
  }

  @Put(':id')
  async updateOrganization(
    @Param('id') id: string,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
    @Request() req: any,
  ): Promise<Organization> {
    const currentUser = req.user as Agent;
    
    // Only admins can update organizations
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can update organizations');
    }

    return this.organizationService.update(id, updateOrganizationDto);
  }

  @Delete(':id')
  async deleteOrganization(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    const currentUser = req.user as Agent;
    
    // Only admins can delete organizations
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can delete organizations');
    }

    return this.organizationService.remove(id);
  }
}
EOF

echo "✅ Compilation fixes applied!"
echo "🔨 Now try building the backend: yarn build"
