#!/bin/bash

echo "🔧 Applying compilation fixes..."

cd /Users/<USER>/hat/PSHUB/nestbe/src/agent

# Replace files with fixed versions
echo "📁 Replacing agent.entity.ts..."
cp agent.entity.updated.ts agent.entity.ts

echo "📁 Replacing agent.service.ts..."
cp agent.service.fixed.ts agent.service.ts

echo "📁 Replacing agent.controller.ts..."
cp agent.controller.fixed.ts agent.controller.ts

echo "📁 Replacing DTOs..."
cp dto/create-agent.dto.fixed.ts dto/create-agent.dto.ts
cp dto/update-agent.dto.fixed.ts dto/update-agent.dto.ts

echo "✅ All files replaced!"
echo "🔨 Now try building: cd nestbe && yarn build"
