#!/bin/bash

# PSHUB Start Both Applications Script

echo "🚀 Starting PSHUB Development Environment"
echo "========================================"

# Colors
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to cleanup on exit
cleanup() {
    echo ""
    print_status "🛑 Stopping development servers..."
    
    # Kill background processes
    if [ ! -z "$BACKEND_PID" ]; then
        kill $BACKEND_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
    fi
    
    # Kill any remaining processes on the ports
    lsof -ti:3000 | xargs -r kill -9 2>/dev/null || true
    lsof -ti:5173 | xargs -r kill -9 2>/dev/null || true
    
    print_success "✅ Cleanup complete"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup EXIT INT TERM

# Check if applications are ready
if [ ! -d "nestbe/node_modules" ]; then
    print_warning "Backend dependencies not installed. Run ./complete-fix.sh first."
    exit 1
fi

if [ ! -d "webapp/node_modules" ]; then
    print_warning "Frontend dependencies not installed. Run ./complete-fix.sh first."
    exit 1
fi

# Start backend
print_status "🔧 Starting NestJS Backend..."
cd nestbe
yarn start:dev &
BACKEND_PID=$!
print_success "Backend started (PID: $BACKEND_PID)"

# Wait for backend to start
print_status "⏳ Waiting for backend to initialize..."
sleep 8

# Start frontend
print_status "🎨 Starting Vue.js Frontend..."
cd ../webapp
npm run dev &
FRONTEND_PID=$!
print_success "Frontend started (PID: $FRONTEND_PID)"

# Wait for frontend to start
sleep 5

echo ""
print_success "🎉 Both applications are starting!"
echo ""
echo "📱 Access URLs:"
echo "   Frontend: http://localhost:5173"
echo "   Backend:  http://localhost:3000"
echo "   API Docs: http://localhost:3000/api"
echo ""
echo "📋 Application Features:"
echo "   ✅ Patient Management"
echo "   ✅ Case Management (4-stage progression)"
echo "   ✅ Prescriber Management"
echo "   ✅ Drug Family & Products"
echo "   ✅ Search & Pagination"
echo "   ✅ Real-time Updates"
echo ""
echo "🔧 Troubleshooting:"
echo "   - If backend fails: Check PostgreSQL is running"
echo "   - If frontend fails: Check for port conflicts"
echo "   - View logs above for specific error messages"
echo ""
print_warning "Press Ctrl+C to stop both servers"

# Wait for user to stop
wait
