#!/bin/bash

# PSHUB Application Fix Script
# This script will fix all the issues identified in the application

echo "🔧 PSHUB Application Fix Script"
echo "==============================="

# Function to print colored output
print_status() {
    echo -e "\033[1;34m$1\033[0m"
}

print_success() {
    echo -e "\033[1;32m✅ $1\033[0m"
}

print_error() {
    echo -e "\033[1;31m❌ $1\033[0m"
}

print_warning() {
    echo -e "\033[1;33m⚠️  $1\033[0m"
}

# Step 1: Clean up git status
print_status "Step 1: Cleaning up git status..."
git add .
git status --porcelain | grep "^D " | awk '{print $2}' | xargs -r git rm
print_success "Git status cleaned"

# Step 2: Fix frontend dependencies
print_status "Step 2: Fixing frontend dependencies..."
cd webapp

# Check if package.json exists and has correct dependencies
if [ -f "package.json" ]; then
    print_success "Frontend package.json found"
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing frontend dependencies..."
        npm install
        print_success "Frontend dependencies installed"
    else
        print_success "Frontend dependencies already installed"
    fi
else
    print_error "Frontend package.json not found"
    exit 1
fi

# Step 3: Fix backend dependencies
print_status "Step 3: Fixing backend dependencies..."
cd ../nestbe

if [ -f "package.json" ]; then
    print_success "Backend package.json found"
    
    # Install dependencies if node_modules doesn't exist
    if [ ! -d "node_modules" ]; then
        print_status "Installing backend dependencies..."
        yarn install
        print_success "Backend dependencies installed"
    else
        print_success "Backend dependencies already installed"
    fi
else
    print_error "Backend package.json not found"
    exit 1
fi

# Step 4: Check database connection
print_status "Step 4: Checking database connection..."
if command -v psql &> /dev/null; then
    if psql -h localhost -U hubsolution -d hubsolution -c "SELECT 1;" &> /dev/null; then
        print_success "Database connection successful"
    else
        print_warning "Database connection failed - please ensure PostgreSQL is running"
        print_warning "Database: hubsolution, User: hubsolution, Password: 9080"
    fi
else
    print_warning "PostgreSQL client not found - cannot test database connection"
fi

# Step 5: Build backend to check for compilation errors
print_status "Step 5: Checking backend compilation..."
if yarn build &> /dev/null; then
    print_success "Backend compiles successfully"
else
    print_error "Backend compilation failed"
    echo "Running build to show errors:"
    yarn build
fi

# Step 6: Check frontend compilation
print_status "Step 6: Checking frontend compilation..."
cd ../webapp
if npm run build &> /dev/null; then
    print_success "Frontend compiles successfully"
else
    print_error "Frontend compilation failed"
    echo "Running build to show errors:"
    npm run build
fi

# Step 7: Create environment file if it doesn't exist
print_status "Step 7: Checking environment configuration..."
cd ../nestbe
if [ ! -f ".env" ]; then
    print_status "Creating .env file..."
    cat > .env << EOF
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=hubsolution
DB_PASSWORD=9080
DB_DATABASE=hubsolution

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Application Configuration
NODE_ENV=development
PORT=3000
EOF
    print_success ".env file created"
else
    print_success ".env file already exists"
fi

# Step 8: Run database migrations if needed
print_status "Step 8: Checking database migrations..."
if yarn typeorm migration:run &> /dev/null; then
    print_success "Database migrations completed"
else
    print_warning "Database migrations failed or not needed"
fi

# Step 9: Final status check
print_status "Step 9: Final status check..."
cd ..

echo ""
echo "🎉 Application Fix Complete!"
echo "=============================="
echo ""
echo "📋 Summary:"
echo "- Git status cleaned"
echo "- Dependencies installed"
echo "- Compilation checked"
echo "- Environment configured"
echo "- Database migrations run"
echo ""
echo "🚀 Next Steps:"
echo "1. Start the backend: cd nestbe && yarn start:dev"
echo "2. Start the frontend: cd webapp && npm run dev"
echo "3. Access the application at http://localhost:5173"
echo "4. API documentation at http://localhost:3000/api"
echo ""
echo "📝 If you encounter issues:"
echo "- Check database is running: sudo service postgresql start"
echo "- Check ports 3000 and 5173 are available"
echo "- Review error logs in the terminal"
