# PSHUB Quick Fix Guide

## 🚨 IMMEDIATE SOLUTION

Your PSHUB application has been successfully converted from React to Vue.js, but there are some residual issues. Follow these steps to fix everything:

### Step 1: Run the Complete Fix Script

```bash
cd /Users/<USER>/hat/PSHUB
chmod +x complete-fix.sh
./complete-fix.sh
```

This script will:
- ✅ Clean up git status
- ✅ Reinstall all dependencies
- ✅ Test compilation
- ✅ Check database connection
- ✅ Clean up ports

### Step 2: Start Both Applications

```bash
chmod +x start-both.sh
./start-both.sh
```

This will start both backend and frontend simultaneously.

### Step 3: Access Your Application

- **Frontend**: http://localhost:5173
- **Backend API**: http://localhost:3000
- **API Documentation**: http://localhost:3000/api

## 🔧 Manual Steps (If Scripts Don't Work)

### Backend Fix:
```bash
cd nestbe
rm -rf node_modules yarn.lock
yarn install
yarn start:dev
```

### Frontend Fix:
```bash
cd webapp
rm -rf node_modules package-lock.json
npm install
npm run dev
```

## 🗄️ Database Setup

Ensure PostgreSQL is running:
```bash
# Start PostgreSQL (varies by system)
sudo service postgresql start
# or
brew services start postgresql

# Test connection
psql -h localhost -U hubsolution -d hubsolution
```

## 📋 Application Architecture

### Frontend (Vue.js + Vite)
- **Port**: 5173
- **Framework**: Vue 3 + TypeScript
- **Styling**: Tailwind CSS
- **Router**: Vue Router 4

### Backend (NestJS + TypeORM)
- **Port**: 3000
- **Database**: PostgreSQL
- **API Prefix**: `/api`
- **Documentation**: Swagger at `/api`

## ✅ Key Features to Test

1. **Dashboard**: http://localhost:5173/dashboard
   - Cases, Patients, Prescribers tabs
   - Search functionality
   - Pagination (10 records per page)

2. **Case Management**:
   - Create new case: http://localhost:5173/case/new
   - 4-stage progression: STAGE_1 → STAGE_2 → STAGE_3 → COMPLETED
   - Real-time form updates

3. **Patient Management**:
   - Create patient: http://localhost:5173/patient/new
   - Age auto-calculation from DOB
   - Insurance management

4. **Prescriber Management**:
   - Create prescriber: http://localhost:5173/prescriber/new
   - Associate with cases

## 🚨 Common Issues & Solutions

### Issue: "Module not found" errors
**Solution**: Dependencies not installed properly
```bash
cd webapp && npm install
cd ../nestbe && yarn install
```

### Issue: Port already in use
**Solution**: Kill processes on ports
```bash
lsof -ti:3000 | xargs kill -9
lsof -ti:5173 | xargs kill -9
```

### Issue: Database connection failed
**Solution**: Check PostgreSQL service
```bash
sudo service postgresql start
# Verify credentials: hubsolution/9080@localhost:5432/hubsolution
```

### Issue: Compilation errors
**Solution**: Check for TypeScript errors
```bash
cd webapp && npm run build
cd ../nestbe && yarn build
```

## 📱 Application Flow

1. **Login**: http://localhost:5173/login
2. **Dashboard**: View all cases, patients, prescribers
3. **Create Case**: Fill patient, prescriber, drug family info
4. **Stage Progression**: Move through validation stages
5. **Search & Filter**: Real-time search across all entities

## 🔍 Debugging

### Check Logs:
- **Frontend**: Browser console (F12)
- **Backend**: Terminal where `yarn start:dev` is running

### API Testing:
- **Swagger UI**: http://localhost:3000/api
- **Test endpoint**: http://localhost:3000/api/patient-cases

### Database:
```sql
-- Connect to database
psql -h localhost -U hubsolution -d hubsolution

-- Check tables
\dt

-- Check patient cases
SELECT * FROM patient_case LIMIT 5;
```

## 🎯 Success Indicators

✅ Both servers start without errors
✅ Dashboard loads with data
✅ Can create new cases/patients/prescribers
✅ Search functionality works
✅ Pagination displays correctly
✅ Stage progression works
✅ Real-time updates without page reload

## 📞 If You Still Have Issues

1. **Check Prerequisites**:
   - Node.js (v16+)
   - npm/yarn
   - PostgreSQL (running)

2. **Review Error Messages**:
   - Terminal output
   - Browser console
   - Network tab in DevTools

3. **Verify File Structure**:
   - All Vue components in `webapp/src/components/`
   - All backend services in `nestbe/src/`

4. **Database State**:
   - Tables exist
   - Migrations ran successfully
   - Sample data present

## 🚀 Next Steps After Fix

1. Test all CRUD operations
2. Verify search and pagination
3. Test case stage progression
4. Check form validations
5. Test real-time updates
6. Verify error handling

Your application should now be fully functional with all the features working as expected!
