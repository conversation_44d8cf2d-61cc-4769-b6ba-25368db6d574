import { Injectable, NotFoundException, ForbiddenException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Like } from 'typeorm';
import { Agent, UserRole } from './agent.entity';
import { AgentCaseConnection } from './agent-case-connection.entity';
import { PatientCase } from '../patient-case/entities/patient-case.entity';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';

interface AgentSearchParams {
  name?: string;
  organization?: string;
  skills?: string;
  role?: UserRole;
  page?: number;
  limit?: number;
}

interface PaginatedAgentResponse {
  data: Agent[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

@Injectable()
export class AgentService {
  constructor(
    @InjectRepository(Agent)
    private agentRepository: Repository<Agent>,
    @InjectRepository(AgentCaseConnection)
    private connectionRepository: Repository<AgentCaseConnection>,
    @InjectRepository(PatientCase)
    private patientCaseRepository: Repository<PatientCase>,
  ) {}

  async searchAgents(params: AgentSearchParams, currentUser: Agent): Promise<PaginatedAgentResponse> {
    const queryBuilder = this.agentRepository.createQueryBuilder('agent')
      .leftJoinAndSelect('agent.organization', 'organization')
      .leftJoinAndSelect('agent.caseConnections', 'caseConnections');

    // Organization filtering for agents
    if (currentUser.role === UserRole.AGENT) {
      queryBuilder.andWhere('agent.organization_id = :organizationId', { 
        organizationId: currentUser.organization_id 
      });
    }

    // Search filters
    if (params.name) {
      queryBuilder.andWhere(
        '(agent.firstName ILIKE :name OR agent.lastName ILIKE :name)',
        { name: `%${params.name}%` }
      );
    }

    if (params.skills) {
      queryBuilder.andWhere('agent.completedSkills::text ILIKE :skills', { 
        skills: `%${params.skills}%` 
      });
    }

    if (params.role) {
      queryBuilder.andWhere('agent.role = :role', { role: params.role });
    }

    // Pagination
    const page = params.page || 1;
    const limit = params.limit || 10;
    const skip = (page - 1) * limit;

    queryBuilder.skip(skip).take(limit);

    const [agents, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data: agents,
      meta: {
        total,
        page,
        limit,
        totalPages,
      },
    };
  }

  async getAllAgents(page = 1, limit = 10, currentUser: Agent): Promise<PaginatedAgentResponse> {
    const queryBuilder = this.agentRepository.createQueryBuilder('agent')
      .leftJoinAndSelect('agent.organization', 'organization')
      .leftJoinAndSelect('agent.caseConnections', 'caseConnections');

    // Organization filtering for agents
    if (currentUser.role === UserRole.AGENT) {
      queryBuilder.andWhere('agent.organization_id = :organizationId', { 
        organizationId: currentUser.organization_id 
      });
    }

    const skip = (page - 1) * limit;
    queryBuilder.skip(skip).take(limit);

    const [agents, total] = await queryBuilder.getManyAndCount();
    const totalPages = Math.ceil(total / limit);

    return {
      data: agents,
      meta: {
        total,
        page,
        limit,
        totalPages,
      },
    };
  }

  async getAgentsByRole(role: UserRole, currentUser: Agent): Promise<Agent[]> {
    const queryBuilder = this.agentRepository.createQueryBuilder('agent')
      .leftJoinAndSelect('agent.organization', 'organization')
      .where('agent.role = :role', { role });

    // Organization filtering for agents
    if (currentUser.role === UserRole.AGENT) {
      queryBuilder.andWhere('agent.organization_id = :organizationId', { 
        organizationId: currentUser.organization_id 
      });
    }

    return queryBuilder.getMany();
  }

  async getAllAdmins(): Promise<Agent[]> {
    return this.agentRepository.find({
      where: { role: UserRole.ADMIN },
      relations: ['organization'],
    });
  }

  async createAgent(createAgentDto: CreateAgentDto, currentUser: Agent): Promise<Agent> {
    const organizationId = currentUser.role === UserRole.AGENT ? 
      currentUser.organization_id : 
      createAgentDto.organization_id || 'HUB-1';

    const agent = this.agentRepository.create({
      ...createAgentDto,
      organization_id: organizationId,
    });

    return this.agentRepository.save(agent);
  }

  async updateAgent(
    id: number, 
    updateAgentDto: UpdateAgentDto, 
    currentUser: Agent
  ): Promise<Agent> {
    const agent = await this.agentRepository.findOne({
      where: { id },
      relations: ['organization'],
    });

    if (!agent) {
      throw new NotFoundException(`Agent with ID ${id} not found`);
    }

    // Check permissions
    if (currentUser.role === UserRole.AGENT && agent.organization_id !== currentUser.organization_id) {
      throw new ForbiddenException('You can only edit agents within your organization');
    }

    // Agents cannot change roles
    if (currentUser.role === UserRole.AGENT && updateAgentDto.role) {
      delete updateAgentDto.role;
    }

    Object.assign(agent, updateAgentDto);
    return this.agentRepository.save(agent);
  }

  async getAgentDetails(agentId: number, currentUser: Agent): Promise<Agent> {
    const agent = await this.agentRepository.findOne({
      where: { id: agentId },
      relations: ['organization', 'caseConnections'],
    });

    if (!agent) {
      throw new NotFoundException(`Agent with ID ${agentId} not found`);
    }

    // Check permissions for agents
    if (currentUser.role === UserRole.AGENT && agent.organization_id !== currentUser.organization_id) {
      throw new ForbiddenException('You can only view agents within your organization');
    }

    return agent;
  }

  async assignAgentToCase(agentId: number, caseId: number): Promise<AgentCaseConnection> {
    const agent = await this.agentRepository.findOne({ where: { id: agentId } });
    const patientCase = await this.patientCaseRepository.findOne({ where: { caseId: caseId.toString() } });

    if (!agent || !patientCase) {
      throw new NotFoundException('Agent or Case not found');
    }

    const connection = this.connectionRepository.create({
      agentId,
      caseId,
      status: 'assigned',
    });

    return this.connectionRepository.save(connection);
  }

  async checkCaseAssignment(caseId: number) {
    const connection = await this.connectionRepository.findOne({
      where: { caseId },
      relations: ['agent'],
    });

    if (connection) {
      return {
        isAssigned: true,
        agentName: `${connection.agent.firstName} ${connection.agent.lastName}`,
        agentId: connection.agent.id,
        status: connection.status
      };
    }

    return {
      isAssigned: false
    };
  }

  async deleteAgent(agentId: number, currentUser: Agent): Promise<void> {
    const agent = await this.agentRepository.findOne({ where: { id: agentId } });

    if (!agent) {
      throw new NotFoundException(`Agent with ID ${agentId} not found`);
    }

    // Check permissions
    if (currentUser.role === UserRole.AGENT && agent.organization_id !== currentUser.organization_id) {
      throw new ForbiddenException('You can only delete agents within your organization');
    }

    await this.agentRepository.delete(agentId);
  }
}
