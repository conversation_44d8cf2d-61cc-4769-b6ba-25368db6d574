import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Body, 
  Param, 
  Query, 
  Delete, 
  Patch, 
  UseGuards,
  Request,
  ForbiddenException
} from '@nestjs/common';
import { AgentService } from './agent.service';
import { Agent, UserRole } from './agent.entity';
import { AgentCaseConnection } from './agent-case-connection.entity';
import { CreateAgentDto } from './dto/create-agent.dto';
import { UpdateAgentDto } from './dto/update-agent.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { OrganizationDecorator } from '../common/decorators/organization.decorator';
import { Organization } from '../organization/entities/organization.entity';
import { PatientCase } from '../patient-case/entities/patient-case.entity';

interface PaginatedAgentResponse {
  data: Agent[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

@Controller('agents')
@UseGuards(JwtAuthGuard)
export class AgentController {
  constructor(private readonly agentService: AgentService) {}

  @Get()
  async getAllAgents(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('role') role?: UserRole,
    @Query('name') name?: string,
    @Query('skills') skills?: string,
    @OrganizationDecorator() organization: Organization,
    @Request() req: any,
  ): Promise<PaginatedAgentResponse> {
    const currentUser = req.user as Agent;
    
    // If user is an agent, only show agents from their organization
    const filterOrganization = currentUser.role === UserRole.AGENT ? 
      { id: currentUser.organization_id } as Organization : 
      organization;

    if (name || skills || role) {
      return this.agentService.searchAgents({
        page,
        limit,
        name,
        skills,
        role,
      }, filterOrganization);
    }

    return this.agentService.getAllAgents(page, limit, filterOrganization);
  }

  @Get('admins')
  async getAllAdmins(@Request() req: any): Promise<Agent[]> {
    const currentUser = req.user as Agent;
    
    // Only admins can view admin list
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can view admin list');
    }

    return this.agentService.getAllAdmins();
  }

  @Get('by-role/:role')
  async getAgentsByRole(
    @Param('role') role: UserRole,
    @OrganizationDecorator() organization: Organization,
    @Request() req: any,
  ): Promise<Agent[]> {
    const currentUser = req.user as Agent;
    
    const filterOrganization = currentUser.role === UserRole.AGENT ? 
      { id: currentUser.organization_id } as Organization : 
      organization;

    return this.agentService.getAgentsByRole(role, filterOrganization);
  }

  @Get(':id')
  async getAgentDetails(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<Agent> {
    const currentUser = req.user as Agent;
    return this.agentService.getAgentDetails(parseInt(id), currentUser);
  }

  @Post()
  async createAgent(
    @Body() createAgentDto: CreateAgentDto,
    @OrganizationDecorator() organization: Organization,
    @Request() req: any,
  ): Promise<Agent> {
    const currentUser = req.user as Agent;
    
    // Only admins can create other admins
    if (createAgentDto.role === UserRole.ADMIN && currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can create admin users');
    }

    // Agents can only create agents in their organization
    const targetOrganization = currentUser.role === UserRole.AGENT ? 
      { id: currentUser.organization_id } as Organization : 
      organization;

    return this.agentService.createAgent(createAgentDto, targetOrganization);
  }

  @Patch(':id')
  async updateAgent(
    @Param('id') id: string,
    @Body() updateAgentDto: UpdateAgentDto,
    @Request() req: any,
  ): Promise<Agent> {
    const currentUser = req.user as Agent;
    return this.agentService.updateAgent(parseInt(id), updateAgentDto, currentUser);
  }

  @Post(':agentId/assign/:caseId')
  async assignAgentToCase(
    @Param('agentId') agentId: string,
    @Param('caseId') caseId: string,
  ): Promise<AgentCaseConnection> {
    return this.agentService.assignAgentToCase(
      parseInt(agentId),
      parseInt(caseId),
    );
  }

  @Get('case/:caseId/assignment')
  async checkCaseAssignment(@Param('caseId') caseId: string) {
    return this.agentService.checkCaseAssignment(parseInt(caseId));
  }

  @Delete(':id')
  async deleteAgent(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    const currentUser = req.user as Agent;
    return this.agentService.deleteAgent(parseInt(id), currentUser);
  }
}
