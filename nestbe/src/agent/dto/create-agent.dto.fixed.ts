import { IsEmail, <PERSON><PERSON>tring, IsO<PERSON>al, IsEnum, IsArray, Is<PERSON>umber } from 'class-validator';
import { UserRole } from '../agent.entity';

export class CreateAgentDto {
  @IsEmail()
  email: string;

  @IsString()
  password: string;

  @IsString()
  firstName: string;

  @IsString()
  lastName: string;

  @IsOptional()
  @IsString()
  organization_id?: string;

  @IsOptional()
  @IsEnum(UserRole)
  role?: UserRole;

  @IsOptional()
  @IsNumber()
  aiClassifiedScore?: number;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  completedSkills?: string[];

  @IsOptional()
  @IsNumber()
  completedDuration?: number;

  @IsOptional()
  @IsString()
  address?: string;
}
