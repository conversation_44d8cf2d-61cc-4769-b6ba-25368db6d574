import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
  constructor(private configService: ConfigService) {
    // Try multiple ways to get the JWT secret
    let secret = configService.get<string>('JWT_SECRET');
    
    // Fallback to process.env if ConfigService doesn't work
    if (!secret) {
      secret = process.env.JWT_SECRET;
    }
    
    // Fallback to a default secret for development
    if (!secret) {
      console.warn('JWT_SECRET not found in environment, using default for development');
      secret = '0473853d2c575eca150f3802cafbd035dd04754d4f9796274455b1a15223d116';
    }
    
    console.log('JWT_SECRET loaded:', secret ? 'SUCCESS' : 'FAILED');
    
    super({
      jwtFromRequest: ExtractJwt.fromAuth<PERSON>eaderAsBearerToken(),
      ignoreExpiration: false,
      secretOrKey: secret,
    });
  }

  async validate(payload: any) {
    return { 
      id: payload.sub,
      email: payload.email,
      organization_id: payload.organization_id
    };
  }
}
