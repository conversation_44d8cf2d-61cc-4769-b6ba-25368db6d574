import { 
  Controller, 
  Get, 
  Post, 
  Put, 
  Body, 
  Param, 
  Delete, 
  UseGuards,
  Request,
  ForbiddenException,
  Query
} from '@nestjs/common';
import { OrganizationService } from './organization.service';
import { Organization } from './entities/organization.entity';
import { CreateOrganizationDto } from './dto/create-organization.dto';
import { UpdateOrganizationDto } from './dto/update-organization.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { Agent, UserRole } from '../agent/agent.entity';

interface PaginatedOrganizationResponse {
  data: Organization[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

@Controller('organizations')
@UseGuards(JwtAuthGuard)
export class OrganizationController {
  constructor(private readonly organizationService: OrganizationService) {}

  @Get()
  async getAllOrganizations(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Request() req: any,
  ): Promise<PaginatedOrganizationResponse> {
    const currentUser = req.user as Agent;
    
    // Only admins can view all organizations
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can view all organizations');
    }

    return this.organizationService.findAllPaginated(page, limit);
  }

  @Get('default')
  async getDefaultOrganization(): Promise<Organization> {
    return this.organizationService.getDefaultOrganization();
  }

  @Get(':id')
  async getOrganization(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<Organization> {
    const currentUser = req.user as Agent;
    
    // Agents can only view their own organization
    if (currentUser.role === UserRole.AGENT && currentUser.organization_id !== id) {
      throw new ForbiddenException('You can only view your own organization');
    }

    return this.organizationService.findOne(id);
  }

  @Post()
  async createOrganization(
    @Body() createOrganizationDto: CreateOrganizationDto,
    @Request() req: any,
  ): Promise<Organization> {
    const currentUser = req.user as Agent;
    
    // Only admins can create organizations
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can create organizations');
    }

    return this.organizationService.create(createOrganizationDto);
  }

  @Put(':id')
  async updateOrganization(
    @Param('id') id: string,
    @Body() updateOrganizationDto: UpdateOrganizationDto,
    @Request() req: any,
  ): Promise<Organization> {
    const currentUser = req.user as Agent;
    
    // Only admins can update organizations
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can update organizations');
    }

    return this.organizationService.update(id, updateOrganizationDto);
  }

  @Delete(':id')
  async deleteOrganization(
    @Param('id') id: string,
    @Request() req: any,
  ): Promise<void> {
    const currentUser = req.user as Agent;
    
    // Only admins can delete organizations
    if (currentUser.role !== UserRole.ADMIN) {
      throw new ForbiddenException('Only admins can delete organizations');
    }

    return this.organizationService.remove(id);
  }
}
