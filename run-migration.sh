#!/bin/bash

echo "🔄 Running Database Migration for Role-Based Access Control"
echo "=========================================================="

cd /Users/<USER>/hat/PSHUB/nestbe

# Check if database is accessible
echo "🔍 Checking database connection..."
if PGPASSWORD=9080 psql -h localhost -U hubsolution -d hubsolution -c "SELECT 1;" > /dev/null 2>&1; then
    echo "✅ Database connection successful"
else
    echo "❌ Database connection failed. Please ensure PostgreSQL is running."
    echo "   Database: hubsolution"
    echo "   User: hubsolution"
    echo "   Password: 9080"
    exit 1
fi

# Run TypeORM migration
echo "🔄 Running TypeORM migration..."
if yarn typeorm migration:run; then
    echo "✅ Migration completed successfully"
else
    echo "❌ Migration failed"
    exit 1
fi

# Verify the migration
echo "🔍 Verifying migration results..."
PGPASSWORD=9080 psql -h localhost -U hubsolution -d hubsolution << EOF
-- Check if role and address columns exist
SELECT 
    column_name, 
    data_type, 
    is_nullable, 
    column_default 
FROM information_schema.columns 
WHERE table_name = 'agent' 
AND column_name IN ('role', 'address');

-- Show current agent count and roles
SELECT 
    role, 
    COUNT(*) as count 
FROM agent 
GROUP BY role;
EOF

echo ""
echo "🎉 Migration Complete!"
echo "====================="
echo ""
echo "✅ Added 'role' column (enum: admin, agent)"
echo "✅ Added 'address' column (varchar, nullable)"
echo "✅ Set default role as 'agent' for existing users"
echo ""
echo "🔧 Next Steps:"
echo "1. Create an admin user if needed"
echo "2. Start the backend: yarn start:dev"
echo "3. Test role-based access control"
echo ""
echo "📝 To create a default admin user:"
echo "   psql -h localhost -U hubsolution -d hubsolution"
echo "   INSERT INTO agent (email, password, \"firstName\", \"lastName\", organization_id, role)"
echo "   VALUES ('<EMAIL>', '\$2b\$10\$hashedpassword', 'System', 'Admin', 'HUB-1', 'admin');"
