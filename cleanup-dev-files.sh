#!/bin/bash

echo "🧹 Cleaning up unnecessary development files..."
echo "=============================================="

cd /Users/<USER>/hat/PSHUB

# Remove root level development scripts and documentation
echo "📁 Removing root level development files..."
rm -f QUICK_FIX_GUIDE.md
rm -f ROLE_BASED_ACCESS_IMPLEMENTATION.md
rm -f TROUBLESHOOTING.md
rm -f apply-final-fixes.sh
rm -f apply-fixes.sh
rm -f check-servers.sh
rm -f complete-fix.sh
rm -f emergency-fix.sh
rm -f final-fix.md
rm -f fix-application.sh
rm -f fix-compilation.sh
rm -f fix-jwt-issue.sh
rm -f implement-role-based-access.sh
rm -f quick-fix-compilation.sh
rm -f run-migration.sh
rm -f start-both.sh
rm -f start-dev.sh

echo "✅ Removed root level development files"

# Remove backend development files
echo "📁 Removing backend development files..."
cd nestbe/src

# Remove .updated and .fixed files
rm -f agent/agent.entity.updated.ts
rm -f organization/organization.controller.fixed.ts

# Remove backup files
rm -f patient-case/patient-case.service.ts.bak

# Remove test files (keeping only essential ones)
find . -name "*.spec.ts" -type f -delete

echo "✅ Removed backend development files"

# Remove frontend development files
echo "📁 Removing frontend development files..."
cd ../../webapp/src

# Remove .updated and .fixed files
rm -f components/AgentList.updated.vue
rm -f components/Dashboard.updated.vue
rm -f components/OrganizationSelector.fixed.vue
rm -f contexts/OrganizationContext.fixed.ts
rm -f main.fixed.ts
rm -f router/index.fixed.ts
rm -f views/Login.fixed.vue

# Remove unused components
rm -f components/HelloWorld.vue

echo "✅ Removed frontend development files"

# Clean up build artifacts
echo "📁 Cleaning build artifacts..."
cd ../../

# Remove backend build artifacts
if [ -d "nestbe/dist" ]; then
    rm -rf nestbe/dist
    echo "✅ Removed backend dist folder"
fi

# Remove node_modules if you want to clean them (optional)
# Uncomment the lines below if you want to remove node_modules
# echo "📁 Removing node_modules (optional)..."
# rm -rf nestbe/node_modules
# rm -rf webapp/node_modules
# echo "✅ Removed node_modules folders"

echo ""
echo "🎉 Cleanup Complete!"
echo "==================="
echo ""
echo "✅ Removed development scripts and documentation"
echo "✅ Removed .updated, .fixed, and .bak files"
echo "✅ Removed test files (*.spec.ts)"
echo "✅ Removed unused components"
echo "✅ Removed build artifacts"
echo ""
echo "📝 Remaining essential files:"
echo "   - Source code (.ts, .vue files)"
echo "   - Configuration files (package.json, tsconfig.json, etc.)"
echo "   - Migration files"
echo "   - Documentation (README.md files)"
echo ""
echo "🚀 Your project is now clean and production-ready!"

# Show final directory structure
echo ""
echo "📊 Final project structure:"
echo "=========================="
echo "PSHUB/"
echo "├── nestbe/          # Backend (NestJS)"
echo "│   ├── src/         # Source code"
echo "│   ├── package.json # Dependencies"
echo "│   └── *.json       # Configuration"
echo "└── webapp/          # Frontend (Vue.js)"
echo "    ├── src/         # Source code"
echo "    ├── package.json # Dependencies"
echo "    └── *.json       # Configuration"
