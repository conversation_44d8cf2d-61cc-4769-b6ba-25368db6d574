#!/bin/bash

echo "🔍 PSHUB Server Diagnostic"
echo "=========================="

# Check if we're in the right directory
if [ ! -d "nestbe" ] || [ ! -d "webapp" ]; then
    echo "❌ Please run this script from the PSHUB root directory"
    exit 1
fi

echo ""
echo "📁 Directory Structure:"
ls -la | grep -E "(nestbe|webapp)"

echo ""
echo "🗄️  Database Status:"
if command -v pg_ctl &> /dev/null; then
    pg_ctl status -D /opt/homebrew/var/postgres 2>/dev/null || echo "PostgreSQL not running or not found"
else
    echo "pg_ctl not found - checking brew services"
    brew services list | grep postgresql || echo "PostgreSQL service not found"
fi

echo ""
echo "🔧 Backend Status:"
cd nestbe

# Check if .env exists
if [ -f ".env" ]; then
    echo "✅ .env file exists"
else
    echo "❌ .env file missing"
fi

# Check if node_modules exists
if [ -d "node_modules" ]; then
    echo "✅ node_modules exists"
else
    echo "❌ node_modules missing - run 'yarn install'"
fi

# Check if backend builds
echo "🔨 Testing backend build..."
if yarn build > /dev/null 2>&1; then
    echo "✅ Backend builds successfully"
else
    echo "❌ Backend build failed"
    echo "Run 'cd nestbe && yarn build' to see errors"
fi

# Check if port 3000 is in use
echo "🌐 Checking port 3000..."
if lsof -i :3000 > /dev/null 2>&1; then
    echo "✅ Port 3000 is in use (backend might be running)"
    lsof -i :3000
else
    echo "❌ Port 3000 is free (backend not running)"
fi

cd ../webapp

echo ""
echo "🎨 Frontend Status:"

# Check if node_modules exists
if [ -d "node_modules" ]; then
    echo "✅ node_modules exists"
else
    echo "❌ node_modules missing - run 'npm install'"
fi

# Check if port 5173 is in use
echo "🌐 Checking port 5173..."
if lsof -i :5173 > /dev/null 2>&1; then
    echo "✅ Port 5173 is in use (frontend might be running)"
    lsof -i :5173
else
    echo "❌ Port 5173 is free (frontend not running)"
fi

echo ""
echo "🚀 Quick Start Commands:"
echo "========================"
echo "1. Start Backend:  cd nestbe && yarn start:dev"
echo "2. Start Frontend: cd webapp && npm run dev"
echo ""
echo "🌐 URLs:"
echo "Backend:  http://localhost:3000"
echo "Frontend: http://localhost:5173"
