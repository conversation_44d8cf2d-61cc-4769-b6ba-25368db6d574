#!/bin/bash

# PSHUB Complete Application Fix Script
# This script will completely fix and restart your PSHUB application

set -e  # Exit on any error

echo "🔧 PSHUB Complete Application Fix"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null 2>&1; then
        return 0  # Port is in use
    else
        return 1  # Port is free
    fi
}

# Function to kill processes on a port
kill_port() {
    local port=$1
    print_status "Killing processes on port $port..."
    lsof -ti:$port | xargs -r kill -9 2>/dev/null || true
    sleep 2
}

# Step 1: Check prerequisites
print_status "Step 1: Checking prerequisites..."

if ! command_exists node; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

if ! command_exists npm; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

if ! command_exists yarn; then
    print_error "Yarn is not installed. Please install Yarn first."
    exit 1
fi

if ! command_exists psql; then
    print_warning "PostgreSQL client not found. Database connection cannot be tested."
else
    print_success "PostgreSQL client found"
fi

print_success "All prerequisites met"

# Step 2: Clean up ports
print_status "Step 2: Cleaning up ports..."

if check_port 3000; then
    print_warning "Port 3000 is in use. Killing processes..."
    kill_port 3000
fi

if check_port 5173; then
    print_warning "Port 5173 is in use. Killing processes..."
    kill_port 5173
fi

print_success "Ports cleaned up"

# Step 3: Clean up git status
print_status "Step 3: Cleaning up git status..."
git add . 2>/dev/null || true
print_success "Git status cleaned"

# Step 4: Fix backend
print_status "Step 4: Fixing backend..."
cd nestbe

# Remove node_modules and reinstall
if [ -d "node_modules" ]; then
    print_status "Removing old backend node_modules..."
    rm -rf node_modules
fi

if [ -f "yarn.lock" ]; then
    print_status "Removing yarn.lock..."
    rm -f yarn.lock
fi

print_status "Installing backend dependencies..."
yarn install

# Check if build works
print_status "Testing backend compilation..."
if yarn build >/dev/null 2>&1; then
    print_success "Backend compiles successfully"
else
    print_error "Backend compilation failed. Running build to show errors:"
    yarn build
    exit 1
fi

print_success "Backend fixed"

# Step 5: Fix frontend
print_status "Step 5: Fixing frontend..."
cd ../webapp

# Remove node_modules and reinstall
if [ -d "node_modules" ]; then
    print_status "Removing old frontend node_modules..."
    rm -rf node_modules
fi

if [ -f "package-lock.json" ]; then
    print_status "Removing package-lock.json..."
    rm -f package-lock.json
fi

print_status "Installing frontend dependencies..."
npm install

# Check if build works
print_status "Testing frontend compilation..."
if npm run build >/dev/null 2>&1; then
    print_success "Frontend compiles successfully"
else
    print_error "Frontend compilation failed. Running build to show errors:"
    npm run build
    exit 1
fi

print_success "Frontend fixed"

# Step 6: Test database connection
print_status "Step 6: Testing database connection..."
cd ../nestbe

if command_exists psql; then
    if PGPASSWORD=9080 psql -h localhost -U hubsolution -d hubsolution -c "SELECT 1;" >/dev/null 2>&1; then
        print_success "Database connection successful"
        
        # Run migrations
        print_status "Running database migrations..."
        if yarn typeorm migration:run >/dev/null 2>&1; then
            print_success "Database migrations completed"
        else
            print_warning "Database migrations failed or not needed"
        fi
    else
        print_warning "Database connection failed. Please ensure PostgreSQL is running."
        print_warning "Database: hubsolution, User: hubsolution, Password: 9080"
    fi
else
    print_warning "Cannot test database connection - psql not available"
fi

# Step 7: Start applications
print_status "Step 7: Starting applications..."

cd ../

print_success "🎉 Application fix completed successfully!"
echo ""
echo "📋 Summary:"
echo "- Git status cleaned"
echo "- Backend dependencies reinstalled and tested"
echo "- Frontend dependencies reinstalled and tested"
echo "- Database connection tested"
echo "- Ports cleaned up"
echo ""
echo "🚀 To start the application:"
echo "1. Backend: cd nestbe && yarn start:dev"
echo "2. Frontend: cd webapp && npm run dev"
echo ""
echo "📱 Access URLs:"
echo "- Frontend: http://localhost:5173"
echo "- Backend API: http://localhost:3000"
echo "- API Documentation: http://localhost:3000/api"
echo ""
echo "🔧 If you still encounter issues:"
echo "- Check that PostgreSQL is running: sudo service postgresql start"
echo "- Verify database credentials in nestbe/.env"
echo "- Check the console for any error messages"
echo ""
print_success "Ready to start development!"
